package com.clothingstore.view;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

/**
 * Controller for Inventory Management page Follows JavaFX 17.0.2 compatibility
 * patterns with programmatic UI creation
 */
public class InventoryManagementController implements Initializable {

    // FXML injected components (minimal approach)
    @FXML
    private VBox mainContainer;

    // Services and DAOs
    private ProductDAO productDAO;

    // Data collections
    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;

    // Programmatically created UI components
    private ScrollPane mainScrollPane;
    private VBox contentContainer;
    private VBox headerSection;
    private VBox filtersSection;
    private VBox summarySection;
    private VBox tableSection;
    private VBox actionsSection;

    // Header components
    private Label titleLabel;
    private Button btnRefresh;
    private Button btnAddProduct;
    private Button btnExport;

    // Filter components
    private TextField txtSearch;
    private ComboBox<String> cmbCategory;
    private ComboBox<String> cmbStockStatus;
    private Button btnClearFilters;

    // Summary components
    private Label lblTotalProducts;
    private Label lblTotalValue;
    private Label lblLowStockCount;
    private Label lblOutOfStockCount;

    // Table components
    private TableView<Product> tblProducts;
    private TableColumn<Product, String> colProductName;
    private TableColumn<Product, String> colCategory;
    private TableColumn<Product, BigDecimal> colPrice;
    private TableColumn<Product, Integer> colStock;
    private TableColumn<Product, String> colStatus;
    private TableColumn<Product, String> colActions;

    // Action components
    private Button btnBulkUpdate;
    private Button btnLowStockReport;
    private Button btnStockAdjustment;

    // Formatters
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("DEBUG: Initializing InventoryManagementController...");

        try {
            // Initialize services and data
            initializeServices();
            initializeData();
            initializeFormatters();

            // Create programmatic UI
            createProgrammaticUI();

            // Load initial data
            loadInventoryData();

            System.out.println("DEBUG: InventoryManagementController initialization completed");
        } catch (Exception e) {
            System.err.println("ERROR: Failed to initialize InventoryManagementController: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Initialization Error", "Failed to initialize Inventory Management: " + e.getMessage());
        }
    }

    /**
     * Initialize services and DAOs
     */
    private void initializeServices() {
        productDAO = ProductDAO.getInstance();
        System.out.println("DEBUG: Inventory management services initialized");
    }

    /**
     * Initialize data collections
     */
    private void initializeData() {
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        System.out.println("DEBUG: Data collections initialized");
    }

    /**
     * Initialize formatters
     */
    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        System.out.println("DEBUG: Formatters initialized");
    }

    /**
     * Create programmatic UI components
     */
    private void createProgrammaticUI() {
        System.out.println("DEBUG: Creating programmatic UI for inventory management...");

        // Clear any existing content
        mainContainer.getChildren().clear();

        // Create main scroll pane
        createMainScrollPane();

        // Create content container
        createContentContainer();

        // Create all sections
        createHeaderSection();
        createFiltersSection();
        createSummarySection();
        createTableSection();
        createActionsSection();

        // Add all sections to content container
        contentContainer.getChildren().addAll(
                headerSection,
                filtersSection,
                summarySection,
                tableSection,
                actionsSection
        );

        // Set up scroll pane
        mainScrollPane.setContent(contentContainer);
        mainContainer.getChildren().add(mainScrollPane);

        System.out.println("DEBUG: Programmatic UI creation completed");
    }

    /**
     * Create main scroll pane for responsive design
     */
    private void createMainScrollPane() {
        mainScrollPane = new ScrollPane();
        mainScrollPane.setFitToWidth(true);
        mainScrollPane.setFitToHeight(true);
        mainScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        mainScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        mainScrollPane.getStyleClass().add("main-scroll-pane");
    }

    /**
     * Create main content container
     */
    private void createContentContainer() {
        contentContainer = new VBox();
        contentContainer.setSpacing(20.0);
        contentContainer.setPadding(new Insets(20.0));
        contentContainer.getStyleClass().add("content-container");
    }

    /**
     * Create header section with title and main action buttons
     */
    private void createHeaderSection() {
        headerSection = new VBox();
        headerSection.setSpacing(15.0);
        headerSection.getStyleClass().add("header-section");

        // Title and main actions container
        HBox titleContainer = new HBox();
        titleContainer.setAlignment(Pos.CENTER_LEFT);
        titleContainer.setSpacing(20.0);

        // Title
        titleLabel = new Label("Inventory Management");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 24.0));
        titleLabel.getStyleClass().add("page-title");

        // Spacer
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        // Action buttons
        HBox actionButtons = new HBox();
        actionButtons.setSpacing(10.0);
        actionButtons.setAlignment(Pos.CENTER_RIGHT);

        btnRefresh = new Button("Refresh");
        btnRefresh.getStyleClass().addAll("btn", "btn-primary");
        btnRefresh.setStyle("-fx-background-color: #3498db; -fx-text-fill: white;");

        btnAddProduct = new Button("Add Product");
        btnAddProduct.getStyleClass().addAll("btn", "btn-success");
        btnAddProduct.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white;");

        btnExport = new Button("Export");
        btnExport.getStyleClass().addAll("btn", "btn-secondary");
        btnExport.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white;");

        actionButtons.getChildren().addAll(btnRefresh, btnAddProduct, btnExport);

        titleContainer.getChildren().addAll(titleLabel, spacer, actionButtons);
        headerSection.getChildren().add(titleContainer);

        // Setup event handlers
        setupHeaderEventHandlers();
    }

    /**
     * Setup event handlers for header buttons
     */
    private void setupHeaderEventHandlers() {
        btnRefresh.setOnAction(e -> handleRefresh());
        btnAddProduct.setOnAction(e -> handleAddProduct());
        btnExport.setOnAction(e -> handleExport());
    }

    /**
     * Create filters section
     */
    private void createFiltersSection() {
        filtersSection = new VBox();
        filtersSection.setSpacing(15.0);
        filtersSection.getStyleClass().add("filters-section");

        // Filters title
        Label filtersTitle = new Label("Filters & Search");
        filtersTitle.setFont(Font.font("System", FontWeight.BOLD, 16.0));
        filtersTitle.getStyleClass().add("section-title");

        // Search and filters container
        HBox searchContainer = new HBox();
        searchContainer.setSpacing(15.0);
        searchContainer.setAlignment(Pos.CENTER_LEFT);

        // Search field
        txtSearch = new TextField();
        txtSearch.setPromptText("Search products...");
        txtSearch.setPrefWidth(200.0);
        txtSearch.getStyleClass().add("search-field");

        // Category filter
        cmbCategory = new ComboBox<>();
        cmbCategory.setPromptText("All Categories");
        cmbCategory.setPrefWidth(150.0);

        // Stock status filter
        cmbStockStatus = new ComboBox<>();
        cmbStockStatus.setPromptText("All Stock Levels");
        cmbStockStatus.setPrefWidth(150.0);
        cmbStockStatus.getItems().addAll("All Stock Levels", "In Stock", "Low Stock", "Out of Stock");

        // Clear filters button
        btnClearFilters = new Button("Clear Filters");
        btnClearFilters.getStyleClass().addAll("btn", "btn-outline");
        btnClearFilters.setStyle("-fx-border-color: #95a5a6; -fx-text-fill: #95a5a6;");

        searchContainer.getChildren().addAll(txtSearch, cmbCategory, cmbStockStatus, btnClearFilters);

        filtersSection.getChildren().addAll(filtersTitle, searchContainer);

        // Setup event handlers
        setupFiltersEventHandlers();
    }

    /**
     * Setup event handlers for filters
     */
    private void setupFiltersEventHandlers() {
        txtSearch.textProperty().addListener((obs, oldText, newText) -> applyFilters());
        cmbCategory.setOnAction(e -> applyFilters());
        cmbStockStatus.setOnAction(e -> applyFilters());
        btnClearFilters.setOnAction(e -> handleClearFilters());
    }

    /**
     * Create summary section with key metrics
     */
    private void createSummarySection() {
        summarySection = new VBox();
        summarySection.setSpacing(15.0);
        summarySection.getStyleClass().add("summary-section");

        // Summary title
        Label summaryTitle = new Label("Inventory Summary");
        summaryTitle.setFont(Font.font("System", FontWeight.BOLD, 16.0));
        summaryTitle.getStyleClass().add("section-title");

        // Summary cards container
        HBox summaryCards = new HBox();
        summaryCards.setSpacing(15.0);
        summaryCards.setAlignment(Pos.CENTER_LEFT);

        // Total products card
        VBox totalProductsCard = createSummaryCard("Total Products", "Loading...");
        lblTotalProducts = (Label) ((VBox) totalProductsCard.getChildren().get(1)).getChildren().get(0);

        // Total value card
        VBox totalValueCard = createSummaryCard("Total Value", "Loading...");
        lblTotalValue = (Label) ((VBox) totalValueCard.getChildren().get(1)).getChildren().get(0);

        // Low stock card
        VBox lowStockCard = createSummaryCard("Low Stock Items", "Loading...");
        lblLowStockCount = (Label) ((VBox) lowStockCard.getChildren().get(1)).getChildren().get(0);

        // Out of stock card
        VBox outOfStockCard = createSummaryCard("Out of Stock", "Loading...");
        lblOutOfStockCount = (Label) ((VBox) outOfStockCard.getChildren().get(1)).getChildren().get(0);

        summaryCards.getChildren().addAll(totalProductsCard, totalValueCard, lowStockCard, outOfStockCard);
        summarySection.getChildren().addAll(summaryTitle, summaryCards);
    }

    /**
     * Create a summary card with title and value
     */
    private VBox createSummaryCard(String title, String value) {
        VBox card = new VBox();
        card.setSpacing(5.0);
        card.setPadding(new Insets(15.0));
        card.getStyleClass().add("summary-card");
        card.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        card.setPrefWidth(180.0);

        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("System", FontWeight.NORMAL, 12.0));
        titleLabel.getStyleClass().add("card-title");
        titleLabel.setStyle("-fx-text-fill: #6c757d;");

        VBox valueContainer = new VBox();
        Label valueLabel = new Label(value);
        valueLabel.setFont(Font.font("System", FontWeight.BOLD, 18.0));
        valueLabel.getStyleClass().add("card-value");
        valueLabel.setStyle("-fx-text-fill: #495057;");
        valueContainer.getChildren().add(valueLabel);

        card.getChildren().addAll(titleLabel, valueContainer);
        return card;
    }

    /**
     * Create table section with products table
     */
    private void createTableSection() {
        tableSection = new VBox();
        tableSection.setSpacing(15.0);
        tableSection.getStyleClass().add("table-section");

        // Table title
        Label tableTitle = new Label("Products");
        tableTitle.setFont(Font.font("System", FontWeight.BOLD, 16.0));
        tableTitle.getStyleClass().add("section-title");

        // Create table
        createProductsTable();

        tableSection.getChildren().addAll(tableTitle, tblProducts);
    }

    /**
     * Create products table with columns
     */
    private void createProductsTable() {
        tblProducts = new TableView<>();
        tblProducts.setPrefHeight(400.0);
        tblProducts.getStyleClass().add("products-table");

        // Product name column
        colProductName = new TableColumn<>("Product Name");
        colProductName.setPrefWidth(200.0);
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));

        // Category column
        colCategory = new TableColumn<>("Category");
        colCategory.setPrefWidth(120.0);
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));

        // Price column
        colPrice = new TableColumn<>("Price");
        colPrice.setPrefWidth(100.0);
        colPrice.setCellValueFactory(new PropertyValueFactory<>("price"));
        colPrice.setCellFactory(column -> new TableCell<Product, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(currencyFormat.format(price));
                }
            }
        });

        // Stock column
        colStock = new TableColumn<>("Stock");
        colStock.setPrefWidth(80.0);
        colStock.setCellValueFactory(new PropertyValueFactory<>("stockQuantity"));

        // Status column
        colStatus = new TableColumn<>("Status");
        colStatus.setPrefWidth(100.0);
        colStatus.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            String status;
            if (product.getStockQuantity() == 0) {
                status = "Out of Stock";
            } else if (product.isLowStock()) {
                status = "Low Stock";
            } else {
                status = "In Stock";
            }
            return new javafx.beans.property.SimpleStringProperty(status);
        });

        // Actions column
        colActions = new TableColumn<>("Actions");
        colActions.setPrefWidth(150.0);
        colActions.setCellFactory(column -> createActionsCell());

        tblProducts.getColumns().addAll(colProductName, colCategory, colPrice, colStock, colStatus, colActions);
        tblProducts.setItems(filteredProducts);
    }

    /**
     * Create actions cell for table rows
     */
    private TableCell<Product, String> createActionsCell() {
        return new TableCell<Product, String>() {
            private final Button editBtn = new Button("Edit");
            private final Button adjustBtn = new Button("Adjust");
            private final HBox container = new HBox(5.0);

            {
                editBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");
                adjustBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-size: 10px;");

                editBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    InventoryManagementController.this.handleEditProduct(product);
                });

                adjustBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    InventoryManagementController.this.handleStockAdjustment(product);
                });

                container.getChildren().addAll(editBtn, adjustBtn);
                container.setAlignment(Pos.CENTER);
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(container);
                }
            }
        };
    }

    /**
     * Create actions section with bulk operations
     */
    private void createActionsSection() {
        actionsSection = new VBox();
        actionsSection.setSpacing(15.0);
        actionsSection.getStyleClass().add("actions-section");

        // Actions title
        Label actionsTitle = new Label("Bulk Operations");
        actionsTitle.setFont(Font.font("System", FontWeight.BOLD, 16.0));
        actionsTitle.getStyleClass().add("section-title");

        // Actions buttons container
        HBox actionsButtons = new HBox();
        actionsButtons.setSpacing(15.0);
        actionsButtons.setAlignment(Pos.CENTER_LEFT);

        btnBulkUpdate = new Button("Bulk Update");
        btnBulkUpdate.getStyleClass().addAll("btn", "btn-warning");
        btnBulkUpdate.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white;");

        btnLowStockReport = new Button("Low Stock Report");
        btnLowStockReport.getStyleClass().addAll("btn", "btn-info");
        btnLowStockReport.setStyle("-fx-background-color: #3498db; -fx-text-fill: white;");

        btnStockAdjustment = new Button("Stock Adjustment");
        btnStockAdjustment.getStyleClass().addAll("btn", "btn-secondary");
        btnStockAdjustment.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white;");

        actionsButtons.getChildren().addAll(btnBulkUpdate, btnLowStockReport, btnStockAdjustment);
        actionsSection.getChildren().addAll(actionsTitle, actionsButtons);

        // Setup event handlers
        setupActionsEventHandlers();
    }

    /**
     * Setup event handlers for action buttons
     */
    private void setupActionsEventHandlers() {
        btnBulkUpdate.setOnAction(e -> handleBulkUpdate());
        btnLowStockReport.setOnAction(e -> handleLowStockReport());
        btnStockAdjustment.setOnAction(e -> handleGlobalStockAdjustment());
    }

    /**
     * Load inventory data from database
     */
    private void loadInventoryData() {
        System.out.println("DEBUG: Loading inventory data...");

        CompletableFuture.runAsync(() -> {
            try {
                List<Product> products = productDAO.findAll();

                Platform.runLater(() -> {
                    allProducts.setAll(products);
                    applyFilters();
                    updateSummaryMetrics();
                    loadCategories();
                    System.out.println("DEBUG: Successfully loaded " + products.size() + " products");
                });

            } catch (SQLException e) {
                Platform.runLater(() -> {
                    System.err.println("ERROR: Failed to load inventory data: " + e.getMessage());
                    AlertUtil.showError("Load Error", "Failed to load inventory data: " + e.getMessage());
                });
            }
        });
    }

    /**
     * Load categories for filter dropdown
     */
    private void loadCategories() {
        try {
            List<String> categories = productDAO.getAllCategories();
            cmbCategory.getItems().clear();
            cmbCategory.getItems().add("All Categories");
            cmbCategory.getItems().addAll(categories);
            cmbCategory.setValue("All Categories");
        } catch (SQLException e) {
            System.err.println("ERROR: Failed to load categories: " + e.getMessage());
        }
    }

    /**
     * Apply filters to product list
     */
    private void applyFilters() {
        String searchText = txtSearch.getText().toLowerCase().trim();
        String selectedCategory = cmbCategory.getValue();
        String selectedStatus = cmbStockStatus.getValue();

        List<Product> filtered = allProducts.stream()
                .filter(product -> {
                    // Search filter
                    if (!searchText.isEmpty()
                            && !product.getName().toLowerCase().contains(searchText)
                            && !product.getCategory().toLowerCase().contains(searchText)) {
                        return false;
                    }

                    // Category filter
                    if (selectedCategory != null && !"All Categories".equals(selectedCategory)
                            && !selectedCategory.equals(product.getCategory())) {
                        return false;
                    }

                    // Status filter
                    if (selectedStatus != null && !"All Stock Levels".equals(selectedStatus)) {
                        switch (selectedStatus) {
                            case "In Stock":
                                return product.getStockQuantity() > 0 && !product.isLowStock();
                            case "Low Stock":
                                return product.isLowStock() && product.getStockQuantity() > 0;
                            case "Out of Stock":
                                return product.getStockQuantity() == 0;
                        }
                    }

                    return true;
                })
                .collect(java.util.stream.Collectors.toList());

        filteredProducts.setAll(filtered);
        updateSummaryMetrics();
    }

    /**
     * Update summary metrics
     */
    private void updateSummaryMetrics() {
        int totalProducts = allProducts.size();
        BigDecimal totalValue = allProducts.stream()
                .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        long lowStockCount = allProducts.stream()
                .filter(p -> p.isLowStock() && p.getStockQuantity() > 0)
                .count();

        long outOfStockCount = allProducts.stream()
                .filter(p -> p.getStockQuantity() == 0)
                .count();

        lblTotalProducts.setText(String.valueOf(totalProducts));
        lblTotalValue.setText(currencyFormat.format(totalValue));
        lblLowStockCount.setText(String.valueOf(lowStockCount));
        lblOutOfStockCount.setText(String.valueOf(outOfStockCount));
    }

    // Event Handlers
    /**
     * Handle refresh button click
     */
    private void handleRefresh() {
        System.out.println("DEBUG: Refreshing inventory data...");
        loadInventoryData();
    }

    /**
     * Handle add product button click
     */
    private void handleAddProduct() {
        AlertUtil.showInfo("Add Product", "Add Product functionality will be implemented in a future update.");
    }

    /**
     * Handle export button click
     */
    private void handleExport() {
        AlertUtil.showInfo("Export", "Export functionality will be implemented in a future update.");
    }

    /**
     * Handle clear filters button click
     */
    private void handleClearFilters() {
        txtSearch.clear();
        cmbCategory.setValue("All Categories");
        cmbStockStatus.setValue("All Stock Levels");
        applyFilters();
    }

    /**
     * Handle edit product action
     */
    private void handleEditProduct(Product product) {
        AlertUtil.showInfo("Edit Product", "Edit functionality for " + product.getName() + " will be implemented in a future update.");
    }

    /**
     * Handle stock adjustment for specific product
     */
    private void handleStockAdjustment(Product product) {
        AlertUtil.showInfo("Stock Adjustment", "Stock adjustment for " + product.getName() + " will be implemented in a future update.");
    }

    /**
     * Handle bulk update action
     */
    private void handleBulkUpdate() {
        AlertUtil.showInfo("Bulk Update", "Bulk update functionality will be implemented in a future update.");
    }

    /**
     * Handle low stock report action
     */
    private void handleLowStockReport() {
        AlertUtil.showInfo("Low Stock Report", "Low stock report functionality will be implemented in a future update.");
    }

    /**
     * Handle global stock adjustment action
     */
    private void handleGlobalStockAdjustment() {
        AlertUtil.showInfo("Stock Adjustment", "Global stock adjustment functionality will be implemented in a future update.");
    }
}
