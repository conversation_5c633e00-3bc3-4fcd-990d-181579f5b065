package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.util.AlertUtil;
import com.clothingstore.util.NavigationUtil;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.LineChart;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.PieChart;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

/**
 * Comprehensive Dashboard Controller for Clothing Store POS System
 *
 * Features: - Real-time business metrics display - Sales summary
 * (daily/weekly/monthly) - Outstanding balances overview - Inventory status and
 * low stock alerts - Customer statistics and analytics - Payment trends and
 * analytics - Quick action buttons for common tasks - Visual charts and data
 * visualization - Responsive layout design
 *
 * Technical Implementation: - JavaFX 17.0.2 compatibility with programmatic UI
 * creation - Integration with existing services and DAOs - Proper error
 * handling and loading states - Navigation system integration
 */
public class ComprehensiveDashboardController implements Initializable {

    // Main container (minimal FXML approach)
    @FXML
    private VBox mainContainer;

    // Programmatically created UI sections
    private ScrollPane mainScrollPane;
    private VBox contentContainer;
    private VBox headerSection;
    private GridPane metricsCardsGrid;
    private HBox quickActionsSection;
    private VBox chartsSection;
    private VBox recentActivitySection;

    // Metric Cards Components
    private Label lblTotalSalesToday;
    private Label lblTotalSalesWeek;
    private Label lblTotalSalesMonth;
    private Label lblOutstandingCount;
    private Label lblOutstandingAmount;
    private Label lblTotalCustomers;
    private Label lblActiveCustomers;
    private Label lblTotalProducts;
    private Label lblLowStockCount;
    private Label lblRecentTransactions;

    // Charts
    private LineChart<String, Number> salesTrendChart;
    private BarChart<String, Number> paymentMethodChart;
    private PieChart inventoryStatusChart;
    private BarChart<String, Number> topProductsChart;

    // Recent Activity Tables
    private TableView<Transaction> recentTransactionsTable;
    private TableView<Customer> topCustomersTable;
    private TableView<Product> lowStockTable;

    // Quick Action Buttons
    private Button btnNewSale;
    private Button btnCustomerManagement;
    private Button btnProductManagement;
    private Button btnInventoryManagement;
    private Button btnTransactionHistory;
    private Button btnOutstandingBalances;
    private Button btnReports;
    private Button btnSettings;

    // Services and DAOs
    private TransactionDAO transactionDAO;
    private CustomerDAO customerDAO;
    private ProductDAO productDAO;
    private PaymentHistoryService paymentHistoryService;

    // Data Collections
    private ObservableList<Transaction> recentTransactions;
    private ObservableList<Customer> topCustomers;
    private ObservableList<Product> lowStockProducts;

    // Formatters
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;
    private DateTimeFormatter dateFormatter;

    // Dashboard State
    private boolean isLoading = false;
    private LocalDateTime lastRefresh;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("DEBUG: Initializing ComprehensiveDashboardController...");

            // Initialize services and formatters
            initializeServices();
            initializeFormatters();
            initializeDataCollections();

            // Create programmatic UI (JavaFX 17.0.2 compatibility)
            createProgrammaticUI();

            // Load initial data
            loadDashboardDataAsync();

            System.out.println("DEBUG: ComprehensiveDashboardController initialization completed");

        } catch (Exception e) {
            System.err.println("ERROR: Failed to initialize ComprehensiveDashboardController: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Dashboard Initialization Error",
                    "Failed to initialize dashboard: " + e.getMessage());
        }
    }

    /**
     * Initialize all services and DAOs
     */
    private void initializeServices() {
        System.out.println("DEBUG: Initializing dashboard services...");

        transactionDAO = TransactionDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        productDAO = ProductDAO.getInstance();
        paymentHistoryService = PaymentHistoryService.getInstance();

        System.out.println("DEBUG: All dashboard services initialized successfully");
    }

    /**
     * Initialize formatters for currency and date display
     */
    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
    }

    /**
     * Initialize data collections
     */
    private void initializeDataCollections() {
        recentTransactions = FXCollections.observableArrayList();
        topCustomers = FXCollections.observableArrayList();
        lowStockProducts = FXCollections.observableArrayList();
    }

    /**
     * Create UI components programmatically following JavaFX 17.0.2 patterns
     */
    private void createProgrammaticUI() {
        System.out.println("DEBUG: Creating programmatic UI for comprehensive dashboard...");

        if (mainContainer == null) {
            mainContainer = new VBox();
            mainContainer.setSpacing(0);
            mainContainer.setPadding(new Insets(0));
        }

        // Clear any existing content
        mainContainer.getChildren().clear();

        // Create main scroll pane for responsive design
        createMainScrollPane();

        // Create main content container
        createContentContainer();

        // Create all dashboard sections
        createHeaderSection();
        createMetricsCardsSection();
        createQuickActionsSection();
        createChartsSection();
        createRecentActivitySection();

        // Add all sections to content container
        contentContainer.getChildren().addAll(
                headerSection,
                metricsCardsGrid,
                quickActionsSection,
                chartsSection,
                recentActivitySection
        );

        // Set up scroll pane
        mainScrollPane.setContent(contentContainer);
        mainContainer.getChildren().add(mainScrollPane);

        System.out.println("DEBUG: Programmatic UI creation completed");
    }

    /**
     * Create main scroll pane for responsive design
     */
    private void createMainScrollPane() {
        mainScrollPane = new ScrollPane();
        mainScrollPane.setFitToWidth(true);
        mainScrollPane.setFitToHeight(true);
        mainScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        mainScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        mainScrollPane.getStyleClass().add("dashboard-scroll-pane");

        // Make scroll pane fill available space
        VBox.setVgrow(mainScrollPane, Priority.ALWAYS);
    }

    /**
     * Create main content container
     */
    private void createContentContainer() {
        contentContainer = new VBox();
        contentContainer.setSpacing(30.0);
        contentContainer.setPadding(new Insets(0));
        contentContainer.setStyle("-fx-background-color: #ffffff;");
    }

    /**
     * Create header section with title and status
     */
    private void createHeaderSection() {
        headerSection = new VBox();
        headerSection.setSpacing(20.0);
        headerSection.setPadding(new Insets(25, 30, 25, 30));
        headerSection.setStyle("-fx-background-color: linear-gradient(to right, #f8f9fa, #e9ecef); "
                + "-fx-border-color: #dee2e6; -fx-border-width: 0 0 2 0; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

        // Main title with enhanced styling
        Label titleLabel = new Label("Comprehensive Business Dashboard");
        titleLabel.setFont(Font.font("Segoe UI", FontWeight.BOLD, 32));
        titleLabel.setStyle("-fx-text-fill: #2c3e50; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 2, 0, 0, 1);");

        // Subtitle with enhanced styling
        Label subtitleLabel = new Label("Real-time business metrics and analytics");
        subtitleLabel.setFont(Font.font("Segoe UI", FontWeight.NORMAL, 16));
        subtitleLabel.setStyle("-fx-text-fill: #6c757d; -fx-padding: 5 0 0 0;");

        // Status and refresh info with enhanced styling
        HBox statusBox = new HBox();
        statusBox.setSpacing(20.0);
        statusBox.setAlignment(Pos.CENTER_LEFT);
        statusBox.setPadding(new Insets(15, 0, 0, 0));

        Label statusLabel = new Label("LIVE DATA");
        statusLabel.setFont(Font.font("Segoe UI", FontWeight.BOLD, 12));
        statusLabel.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; "
                + "-fx-padding: 6 12 6 12; -fx-background-radius: 15; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);");

        Label refreshLabel = new Label("Last updated: Loading...");
        refreshLabel.setFont(Font.font("Segoe UI", FontWeight.NORMAL, 12));
        refreshLabel.setStyle("-fx-text-fill: #6c757d;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        Button refreshButton = new Button("Refresh Dashboard");
        refreshButton.setFont(Font.font("Segoe UI", FontWeight.MEDIUM, 12));
        refreshButton.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; "
                + "-fx-padding: 8 16 8 16; -fx-background-radius: 6; "
                + "-fx-border-radius: 6; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);");
        refreshButton.setOnMouseEntered(e -> refreshButton.setStyle(
                "-fx-background-color: #0056b3; -fx-text-fill: white; "
                + "-fx-padding: 8 16 8 16; -fx-background-radius: 6; "
                + "-fx-border-radius: 6; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 2);"));
        refreshButton.setOnMouseExited(e -> refreshButton.setStyle(
                "-fx-background-color: #007bff; -fx-text-fill: white; "
                + "-fx-padding: 8 16 8 16; -fx-background-radius: 6; "
                + "-fx-border-radius: 6; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);"));
        refreshButton.setOnAction(e -> refreshDashboard());

        statusBox.getChildren().addAll(statusLabel, refreshLabel, spacer, refreshButton);

        headerSection.getChildren().addAll(titleLabel, subtitleLabel, statusBox);
    }

    /**
     * Create metrics cards section with key business indicators
     */
    private void createMetricsCardsSection() {
        metricsCardsGrid = new GridPane();
        metricsCardsGrid.setHgap(20.0);
        metricsCardsGrid.setVgap(20.0);
        metricsCardsGrid.setPadding(new Insets(25, 30, 25, 30));
        metricsCardsGrid.setStyle("-fx-background-color: #f8f9fa;");

        // Row 1: Sales Metrics
        VBox salesTodayCard = createMetricCard("Today's Sales", "Loading...", "sales-card");
        lblTotalSalesToday = (Label) ((VBox) salesTodayCard.getChildren().get(1)).getChildren().get(0);

        VBox salesWeekCard = createMetricCard("This Week", "Loading...", "sales-card");
        lblTotalSalesWeek = (Label) ((VBox) salesWeekCard.getChildren().get(1)).getChildren().get(0);

        VBox salesMonthCard = createMetricCard("This Month", "Loading...", "sales-card");
        lblTotalSalesMonth = (Label) ((VBox) salesMonthCard.getChildren().get(1)).getChildren().get(0);

        VBox transactionsCard = createMetricCard("🧾 Recent Transactions", "Loading...", "transactions-card");
        lblRecentTransactions = (Label) ((VBox) transactionsCard.getChildren().get(1)).getChildren().get(0);

        // Row 2: Outstanding Balances
        VBox outstandingCountCard = createMetricCard("! Outstanding Count", "Loading...", "outstanding-card");
        lblOutstandingCount = (Label) ((VBox) outstandingCountCard.getChildren().get(1)).getChildren().get(0);

        VBox outstandingAmountCard = createMetricCard("Outstanding Amount", "Loading...", "outstanding-card");
        lblOutstandingAmount = (Label) ((VBox) outstandingAmountCard.getChildren().get(1)).getChildren().get(0);

        // Row 3: Customer Metrics
        VBox totalCustomersCard = createMetricCard("Total Customers", "Loading...", "customer-card");
        lblTotalCustomers = (Label) ((VBox) totalCustomersCard.getChildren().get(1)).getChildren().get(0);

        VBox activeCustomersCard = createMetricCard("✅ Active Customers", "Loading...", "customer-card");
        lblActiveCustomers = (Label) ((VBox) activeCustomersCard.getChildren().get(1)).getChildren().get(0);

        // Row 4: Inventory Metrics
        VBox totalProductsCard = createMetricCard("Total Products", "Loading...", "inventory-card");
        lblTotalProducts = (Label) ((VBox) totalProductsCard.getChildren().get(1)).getChildren().get(0);

        VBox lowStockCard = createMetricCard("! Low Stock Items", "Loading...", "inventory-card");
        lblLowStockCount = (Label) ((VBox) lowStockCard.getChildren().get(1)).getChildren().get(0);

        // Add cards to grid (4 columns)
        metricsCardsGrid.add(salesTodayCard, 0, 0);
        metricsCardsGrid.add(salesWeekCard, 1, 0);
        metricsCardsGrid.add(salesMonthCard, 2, 0);
        metricsCardsGrid.add(transactionsCard, 3, 0);

        metricsCardsGrid.add(outstandingCountCard, 0, 1);
        metricsCardsGrid.add(outstandingAmountCard, 1, 1);
        metricsCardsGrid.add(totalCustomersCard, 2, 1);
        metricsCardsGrid.add(activeCustomersCard, 3, 1);

        metricsCardsGrid.add(totalProductsCard, 0, 2);
        metricsCardsGrid.add(lowStockCard, 1, 2);

        // Make columns grow equally
        for (int i = 0; i < 4; i++) {
            metricsCardsGrid.getColumnConstraints().add(new javafx.scene.layout.ColumnConstraints());
            metricsCardsGrid.getColumnConstraints().get(i).setPercentWidth(25.0);
        }
    }

    /**
     * Create a metric card with icon, title, and value
     */
    private VBox createMetricCard(String title, String value, String styleClass) {
        VBox card = new VBox();
        card.setSpacing(12.0);
        card.setPadding(new Insets(20, 18, 20, 18));
        card.setAlignment(Pos.CENTER_LEFT);
        card.setMinHeight(120);
        card.setPrefHeight(120);

        // Enhanced card styling with gradients and shadows
        String cardColor = getCardColor(styleClass);
        card.setStyle("-fx-background-color: " + cardColor + "; "
                + "-fx-background-radius: 12; "
                + "-fx-border-radius: 12; "
                + "-fx-border-color: rgba(0,0,0,0.1); "
                + "-fx-border-width: 1; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 0, 3);");

        // Add hover effect
        card.setOnMouseEntered(e -> card.setStyle(
                "-fx-background-color: " + cardColor + "; "
                + "-fx-background-radius: 12; "
                + "-fx-border-radius: 12; "
                + "-fx-border-color: rgba(0,0,0,0.2); "
                + "-fx-border-width: 1; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 12, 0, 0, 4); "
                + "-fx-scale-x: 1.02; -fx-scale-y: 1.02;"));
        card.setOnMouseExited(e -> card.setStyle(
                "-fx-background-color: " + cardColor + "; "
                + "-fx-background-radius: 12; "
                + "-fx-border-radius: 12; "
                + "-fx-border-color: rgba(0,0,0,0.1); "
                + "-fx-border-width: 1; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 0, 3); "
                + "-fx-scale-x: 1.0; -fx-scale-y: 1.0;"));

        // Title with enhanced styling
        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("Segoe UI", FontWeight.MEDIUM, 13));
        titleLabel.setStyle("-fx-text-fill: #6c757d; -fx-opacity: 0.9;");

        // Value container with enhanced styling
        VBox valueContainer = new VBox();
        valueContainer.setAlignment(Pos.CENTER_LEFT);
        valueContainer.setSpacing(2);

        Label valueLabel = new Label(value);
        valueLabel.setFont(Font.font("Segoe UI", FontWeight.BOLD, 24));
        valueLabel.setStyle("-fx-text-fill: #2c3e50;");

        valueContainer.getChildren().add(valueLabel);

        card.getChildren().addAll(titleLabel, valueContainer);

        return card;
    }

    /**
     * Get card background color based on style class
     */
    private String getCardColor(String styleClass) {
        switch (styleClass) {
            case "sales-card":
                return "linear-gradient(to bottom right, #e3f2fd, #bbdefb)";
            case "outstanding-card":
                return "linear-gradient(to bottom right, #fff3e0, #ffcc80)";
            case "customer-card":
                return "linear-gradient(to bottom right, #e8f5e8, #c8e6c9)";
            case "inventory-card":
                return "linear-gradient(to bottom right, #f3e5f5, #ce93d8)";
            case "transactions-card":
                return "linear-gradient(to bottom right, #fce4ec, #f8bbd9)";
            default:
                return "linear-gradient(to bottom right, #f8f9fa, #e9ecef)";
        }
    }

    /**
     * Create quick actions section with common task buttons
     */
    private void createQuickActionsSection() {
        quickActionsSection = new HBox();
        quickActionsSection.setSpacing(20.0);
        quickActionsSection.setAlignment(Pos.CENTER);
        quickActionsSection.setPadding(new Insets(25, 30, 25, 30));
        quickActionsSection.setStyle("-fx-background-color: #f8f9fa; "
                + "-fx-border-color: #dee2e6; -fx-border-width: 1 0 1 0;");

        // Section title
        VBox titleContainer = new VBox();
        titleContainer.setSpacing(20.0);

        Label sectionTitle = new Label("Quick Actions");
        sectionTitle.setFont(Font.font("Segoe UI", FontWeight.BOLD, 20));
        sectionTitle.setStyle("-fx-text-fill: #2c3e50;");

        HBox buttonsContainer = new HBox();
        buttonsContainer.setSpacing(15.0);
        buttonsContainer.setAlignment(Pos.CENTER);

        // Create quick action buttons
        btnNewSale = createQuickActionButton("New Sale", "Start a new transaction");
        btnCustomerManagement = createQuickActionButton("Customers", "Manage customer database");
        btnProductManagement = createQuickActionButton("Products", "Manage product inventory");
        btnInventoryManagement = createQuickActionButton("Inventory", "View inventory status");
        btnTransactionHistory = createQuickActionButton("Transactions", "View transaction history");
        btnOutstandingBalances = createQuickActionButton("Outstanding", "Manage outstanding balances");
        btnReports = createQuickActionButton("Reports", "Generate business reports");
        btnSettings = createQuickActionButton("Settings", "System settings");

        // Add event handlers
        setupQuickActionHandlers();

        buttonsContainer.getChildren().addAll(
                btnNewSale, btnCustomerManagement, btnProductManagement, btnInventoryManagement,
                btnTransactionHistory, btnOutstandingBalances, btnReports, btnSettings
        );

        titleContainer.getChildren().addAll(sectionTitle, buttonsContainer);
        quickActionsSection.getChildren().add(titleContainer);
    }

    /**
     * Create a quick action button with enhanced styling
     */
    private Button createQuickActionButton(String text, String tooltip) {
        Button button = new Button(text);
        button.setPrefWidth(130);
        button.setPrefHeight(70);
        button.setFont(Font.font("Segoe UI", FontWeight.MEDIUM, 12));

        // Enhanced button styling
        button.setStyle("-fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa); "
                + "-fx-border-color: #dee2e6; -fx-border-width: 1; "
                + "-fx-border-radius: 8; -fx-background-radius: 8; "
                + "-fx-text-fill: #495057; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);");

        // Add hover and click effects
        button.setOnMouseEntered(e -> button.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #e3f2fd, #bbdefb); "
                + "-fx-border-color: #2196f3; -fx-border-width: 2; "
                + "-fx-border-radius: 8; -fx-background-radius: 8; "
                + "-fx-text-fill: #1976d2; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(33,150,243,0.3), 6, 0, 0, 3);"));

        button.setOnMouseExited(e -> button.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa); "
                + "-fx-border-color: #dee2e6; -fx-border-width: 1; "
                + "-fx-border-radius: 8; -fx-background-radius: 8; "
                + "-fx-text-fill: #495057; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);"));

        button.setOnMousePressed(e -> button.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #1976d2, #1565c0); "
                + "-fx-border-color: #0d47a1; -fx-border-width: 2; "
                + "-fx-border-radius: 8; -fx-background-radius: 8; "
                + "-fx-text-fill: white; -fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(13,71,161,0.4), 3, 0, 0, 1);"));

        // Add tooltip if provided
        if (tooltip != null && !tooltip.isEmpty()) {
            javafx.scene.control.Tooltip tooltipControl = new javafx.scene.control.Tooltip(tooltip);
            tooltipControl.setStyle("-fx-background-color: #2c3e50; -fx-text-fill: white; "
                    + "-fx-background-radius: 6; -fx-font-size: 11px;");
            button.setTooltip(tooltipControl);
        }

        return button;
    }

    /**
     * Setup event handlers for quick action buttons
     */
    private void setupQuickActionHandlers() {
        btnNewSale.setOnAction(e -> NavigationUtil.navigateToPointOfSale(btnNewSale));
        btnCustomerManagement.setOnAction(e -> NavigationUtil.navigateToCustomerManagement(btnCustomerManagement));
        btnProductManagement.setOnAction(e -> NavigationUtil.navigateToProductManagement(btnProductManagement));
        btnInventoryManagement.setOnAction(e -> showInventoryManagement());
        btnTransactionHistory.setOnAction(e -> NavigationUtil.navigateToTransactionHistory(btnTransactionHistory));
        btnOutstandingBalances.setOnAction(e -> showOutstandingBalances());
        btnReports.setOnAction(e -> showReports());
        btnSettings.setOnAction(e -> showSettings());
    }

    /**
     * Create charts section with visual data representation
     */
    private void createChartsSection() {
        chartsSection = new VBox();
        chartsSection.setSpacing(20.0);
        chartsSection.getStyleClass().add("charts-section");

        // Section title
        Label sectionTitle = new Label("Analytics & Trends");
        sectionTitle.setFont(Font.font("System", FontWeight.BOLD, 18));
        sectionTitle.getStyleClass().add("section-title");

        // Charts container
        GridPane chartsGrid = new GridPane();
        chartsGrid.setHgap(20.0);
        chartsGrid.setVgap(20.0);

        // Sales Trend Chart (Line Chart)
        createSalesTrendChart();

        // Payment Methods Chart (Bar Chart)
        createPaymentMethodChart();

        // Inventory Status Chart (Pie Chart)
        createInventoryStatusChart();

        // Top Products Chart (Bar Chart)
        createTopProductsChart();

        // Add charts to grid (2x2 layout)
        chartsGrid.add(salesTrendChart, 0, 0);
        chartsGrid.add(paymentMethodChart, 1, 0);
        chartsGrid.add(inventoryStatusChart, 0, 1);
        chartsGrid.add(topProductsChart, 1, 1);

        // Make columns grow equally
        for (int i = 0; i < 2; i++) {
            chartsGrid.getColumnConstraints().add(new javafx.scene.layout.ColumnConstraints());
            chartsGrid.getColumnConstraints().get(i).setPercentWidth(50.0);
        }

        chartsSection.getChildren().addAll(sectionTitle, chartsGrid);
    }

    /**
     * Create sales trend line chart
     */
    private void createSalesTrendChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Date");
        yAxis.setLabel("Sales Amount");

        salesTrendChart = new LineChart<>(xAxis, yAxis);
        salesTrendChart.setTitle("Sales Trend (Last 7 Days)");
        salesTrendChart.setPrefHeight(300);
        salesTrendChart.setLegendVisible(false);
        salesTrendChart.getStyleClass().add("sales-trend-chart");
    }

    /**
     * Create payment method bar chart
     */
    private void createPaymentMethodChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Payment Method");
        yAxis.setLabel("Transaction Count");

        paymentMethodChart = new BarChart<>(xAxis, yAxis);
        paymentMethodChart.setTitle("Payment Methods Usage");
        paymentMethodChart.setPrefHeight(300);
        paymentMethodChart.setLegendVisible(false);
        paymentMethodChart.getStyleClass().add("payment-method-chart");
    }

    /**
     * Create inventory status pie chart
     */
    private void createInventoryStatusChart() {
        inventoryStatusChart = new PieChart();
        inventoryStatusChart.setTitle("Inventory Status");
        inventoryStatusChart.setPrefHeight(300);
        inventoryStatusChart.setLegendVisible(true);
        inventoryStatusChart.getStyleClass().add("inventory-status-chart");
    }

    /**
     * Create top products bar chart
     */
    private void createTopProductsChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Product");
        yAxis.setLabel("Sales Quantity");

        topProductsChart = new BarChart<>(xAxis, yAxis);
        topProductsChart.setTitle("Top Selling Products");
        topProductsChart.setPrefHeight(300);
        topProductsChart.setLegendVisible(false);
        topProductsChart.getStyleClass().add("top-products-chart");
    }

    /**
     * Create recent activity section with data tables
     */
    private void createRecentActivitySection() {
        recentActivitySection = new VBox();
        recentActivitySection.setSpacing(20.0);
        recentActivitySection.getStyleClass().add("recent-activity-section");

        // Section title
        Label sectionTitle = new Label("Recent Activity");
        sectionTitle.setFont(Font.font("System", FontWeight.BOLD, 18));
        sectionTitle.getStyleClass().add("section-title");

        // Tables container
        HBox tablesContainer = new HBox();
        tablesContainer.setSpacing(20.0);

        // Recent Transactions Table
        VBox transactionsContainer = createRecentTransactionsTable();

        // Top Customers Table
        VBox customersContainer = createTopCustomersTable();

        // Low Stock Table
        VBox lowStockContainer = createLowStockTable();

        tablesContainer.getChildren().addAll(transactionsContainer, customersContainer, lowStockContainer);

        // Make tables grow equally
        HBox.setHgrow(transactionsContainer, Priority.ALWAYS);
        HBox.setHgrow(customersContainer, Priority.ALWAYS);
        HBox.setHgrow(lowStockContainer, Priority.ALWAYS);

        recentActivitySection.getChildren().addAll(sectionTitle, tablesContainer);
    }

    /**
     * Create recent transactions table
     */
    private VBox createRecentTransactionsTable() {
        VBox container = new VBox();
        container.setSpacing(10.0);

        Label tableTitle = new Label("🧾 Recent Transactions");
        tableTitle.setFont(Font.font("System", FontWeight.BOLD, 14));

        recentTransactionsTable = new TableView<>();
        recentTransactionsTable.setPrefHeight(200);
        recentTransactionsTable.setItems(recentTransactions);

        // Columns
        TableColumn<Transaction, String> colTransactionNumber = new TableColumn<>("Transaction #");
        colTransactionNumber.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));
        colTransactionNumber.setPrefWidth(120);

        TableColumn<Transaction, String> colDate = new TableColumn<>("Date");
        colDate.setCellValueFactory(cellData -> {
            LocalDateTime date = cellData.getValue().getTransactionDate();
            return new javafx.beans.property.SimpleStringProperty(
                    date != null ? dateTimeFormatter.format(date) : "N/A"
            );
        });
        colDate.setPrefWidth(140);

        TableColumn<Transaction, String> colAmount = new TableColumn<>("Amount");
        colAmount.setCellValueFactory(cellData -> {
            BigDecimal amount = cellData.getValue().getTotalAmount();
            return new javafx.beans.property.SimpleStringProperty(
                    amount != null ? currencyFormat.format(amount) : "$0.00"
            );
        });
        colAmount.setPrefWidth(100);

        recentTransactionsTable.getColumns().addAll(colTransactionNumber, colDate, colAmount);

        container.getChildren().addAll(tableTitle, recentTransactionsTable);
        return container;
    }

    /**
     * Create top customers table
     */
    private VBox createTopCustomersTable() {
        VBox container = new VBox();
        container.setSpacing(10.0);

        Label tableTitle = new Label("Top Customers");
        tableTitle.setFont(Font.font("System", FontWeight.BOLD, 14));

        topCustomersTable = new TableView<>();
        topCustomersTable.setPrefHeight(200);
        topCustomersTable.setItems(topCustomers);

        // Columns
        TableColumn<Customer, String> colName = new TableColumn<>("Customer");
        colName.setCellValueFactory(cellData -> {
            Customer customer = cellData.getValue();
            String name = (customer.getFirstName() != null ? customer.getFirstName() : "") + " "
                    + (customer.getLastName() != null ? customer.getLastName() : "");
            return new javafx.beans.property.SimpleStringProperty(name.trim());
        });
        colName.setPrefWidth(150);

        TableColumn<Customer, String> colTotalSpent = new TableColumn<>("Total Spent");
        colTotalSpent.setCellValueFactory(cellData -> {
            double totalSpent = cellData.getValue().getTotalSpent();
            return new javafx.beans.property.SimpleStringProperty(currencyFormat.format(totalSpent));
        });
        colTotalSpent.setPrefWidth(100);

        TableColumn<Customer, String> colPurchases = new TableColumn<>("Purchases");
        colPurchases.setCellValueFactory(cellData -> {
            int purchases = cellData.getValue().getTotalPurchases();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(purchases));
        });
        colPurchases.setPrefWidth(80);

        topCustomersTable.getColumns().addAll(colName, colTotalSpent, colPurchases);

        container.getChildren().addAll(tableTitle, topCustomersTable);
        return container;
    }

    /**
     * Create low stock table
     */
    private VBox createLowStockTable() {
        VBox container = new VBox();
        container.setSpacing(10.0);

        Label tableTitle = new Label("! Low Stock Items");
        tableTitle.setFont(Font.font("System", FontWeight.BOLD, 14));

        lowStockTable = new TableView<>();
        lowStockTable.setPrefHeight(200);
        lowStockTable.setItems(lowStockProducts);

        // Columns
        TableColumn<Product, String> colProductName = new TableColumn<>("Product");
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductName.setPrefWidth(150);

        TableColumn<Product, String> colStock = new TableColumn<>("Stock");
        colStock.setCellValueFactory(cellData -> {
            int stock = cellData.getValue().getStockQuantity();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(stock));
        });
        colStock.setPrefWidth(60);

        TableColumn<Product, String> colCategory = new TableColumn<>("Category");
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colCategory.setPrefWidth(100);

        lowStockTable.getColumns().addAll(colProductName, colStock, colCategory);

        container.getChildren().addAll(tableTitle, lowStockTable);
        return container;
    }

    /**
     * Load dashboard data asynchronously
     */
    private void loadDashboardDataAsync() {
        System.out.println("DEBUG: Starting async dashboard data loading...");

        isLoading = true;

        CompletableFuture.runAsync(() -> {
            try {
                // Load all data in background
                loadBusinessMetrics();
                loadRecentTransactions();
                loadTopCustomers();
                loadLowStockProducts();
                loadChartData();

                // Update UI on JavaFX thread
                Platform.runLater(() -> {
                    updateMetricsDisplay();
                    updateChartsDisplay();
                    lastRefresh = LocalDateTime.now();
                    isLoading = false;
                    System.out.println("DEBUG: Dashboard data loading completed successfully");
                });

            } catch (Exception e) {
                System.err.println("ERROR: Failed to load dashboard data: " + e.getMessage());
                e.printStackTrace();

                Platform.runLater(() -> {
                    isLoading = false;
                    AlertUtil.showError("Data Loading Error",
                            "Failed to load dashboard data: " + e.getMessage());
                });
            }
        });
    }

    /**
     * Load business metrics from database
     */
    private void loadBusinessMetrics() throws SQLException {
        System.out.println("DEBUG: Loading business metrics...");

        // Get all transactions for calculations
        List<Transaction> allTransactions = transactionDAO.findAll();

        // Calculate sales metrics
        LocalDate today = LocalDate.now();
        LocalDate weekStart = today.minusDays(7);
        LocalDate monthStart = today.minusDays(30);

        BigDecimal salesToday = calculateSalesForPeriod(allTransactions, today, today);
        BigDecimal salesWeek = calculateSalesForPeriod(allTransactions, weekStart, today);
        BigDecimal salesMonth = calculateSalesForPeriod(allTransactions, monthStart, today);

        // Outstanding balances
        List<Transaction> outstandingTransactions = allTransactions.stream()
                .filter(t -> t.getRemainingBalance() != null && t.getRemainingBalance().compareTo(BigDecimal.ZERO) > 0)
                .collect(java.util.stream.Collectors.toList());

        int outstandingCount = outstandingTransactions.size();
        BigDecimal outstandingAmount = outstandingTransactions.stream()
                .map(Transaction::getRemainingBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Customer metrics
        List<Customer> allCustomers = customerDAO.findAll();
        int totalCustomers = allCustomers.size();
        long activeCustomers = allCustomers.stream()
                .filter(c -> "Active".equals(c.getStatus()))
                .count();

        // Product metrics
        List<Product> allProducts = productDAO.findAll();
        int totalProducts = allProducts.size();
        long lowStockCount = allProducts.stream()
                .filter(p -> p.getStockQuantity() <= p.getMinStockLevel())
                .count();

        // Store metrics for UI update
        Platform.runLater(() -> {
            if (lblTotalSalesToday != null) {
                lblTotalSalesToday.setText(currencyFormat.format(salesToday));
            }
            if (lblTotalSalesWeek != null) {
                lblTotalSalesWeek.setText(currencyFormat.format(salesWeek));
            }
            if (lblTotalSalesMonth != null) {
                lblTotalSalesMonth.setText(currencyFormat.format(salesMonth));
            }
            if (lblOutstandingCount != null) {
                lblOutstandingCount.setText(String.valueOf(outstandingCount));
            }
            if (lblOutstandingAmount != null) {
                lblOutstandingAmount.setText(currencyFormat.format(outstandingAmount));
            }
            if (lblTotalCustomers != null) {
                lblTotalCustomers.setText(String.valueOf(totalCustomers));
            }
            if (lblActiveCustomers != null) {
                lblActiveCustomers.setText(String.valueOf(activeCustomers));
            }
            if (lblTotalProducts != null) {
                lblTotalProducts.setText(String.valueOf(totalProducts));
            }
            if (lblLowStockCount != null) {
                lblLowStockCount.setText(String.valueOf(lowStockCount));
            }
            if (lblRecentTransactions != null) {
                lblRecentTransactions.setText(String.valueOf(Math.min(10, allTransactions.size())));
            }
        });

        System.out.println("DEBUG: Business metrics loaded successfully");
    }

    /**
     * Calculate sales for a specific period
     */
    private BigDecimal calculateSalesForPeriod(List<Transaction> transactions, LocalDate startDate, LocalDate endDate) {
        return transactions.stream()
                .filter(t -> {
                    if (t.getTransactionDate() == null) {
                        return false;
                    }
                    LocalDate transactionDate = t.getTransactionDate().toLocalDate();
                    return !transactionDate.isBefore(startDate) && !transactionDate.isAfter(endDate);
                })
                .map(Transaction::getTotalAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Load recent transactions
     */
    private void loadRecentTransactions() throws SQLException {
        System.out.println("DEBUG: Loading recent transactions...");

        List<Transaction> allTransactions = transactionDAO.findAll();
        List<Transaction> recent = allTransactions.stream()
                .sorted((t1, t2) -> {
                    if (t1.getTransactionDate() == null && t2.getTransactionDate() == null) {
                        return 0;
                    }
                    if (t1.getTransactionDate() == null) {
                        return 1;
                    }
                    if (t2.getTransactionDate() == null) {
                        return -1;
                    }
                    return t2.getTransactionDate().compareTo(t1.getTransactionDate());
                })
                .limit(10)
                .collect(java.util.stream.Collectors.toList());

        Platform.runLater(() -> {
            recentTransactions.clear();
            recentTransactions.addAll(recent);
        });

        System.out.println("DEBUG: Loaded " + recent.size() + " recent transactions");
    }

    /**
     * Load top customers
     */
    private void loadTopCustomers() throws SQLException {
        System.out.println("DEBUG: Loading top customers...");

        List<Customer> topCustomersList = customerDAO.findTopCustomers(10);

        Platform.runLater(() -> {
            topCustomers.clear();
            topCustomers.addAll(topCustomersList);
        });

        System.out.println("DEBUG: Loaded " + topCustomersList.size() + " top customers");
    }

    /**
     * Load low stock products
     */
    private void loadLowStockProducts() throws SQLException {
        System.out.println("DEBUG: Loading low stock products...");

        List<Product> allProducts = productDAO.findAll();
        List<Product> lowStock = allProducts.stream()
                .filter(p -> p.getStockQuantity() <= p.getMinStockLevel())
                .sorted((p1, p2) -> Integer.compare(p1.getStockQuantity(), p2.getStockQuantity()))
                .limit(10)
                .collect(java.util.stream.Collectors.toList());

        Platform.runLater(() -> {
            lowStockProducts.clear();
            lowStockProducts.addAll(lowStock);
        });

        System.out.println("DEBUG: Loaded " + lowStock.size() + " low stock products");
    }

    /**
     * Load chart data
     */
    private void loadChartData() {
        System.out.println("DEBUG: Loading chart data...");
        // Chart data loading will be implemented in future iterations
        // For now, we'll create sample data to demonstrate functionality
    }

    /**
     * Update metrics display
     */
    private void updateMetricsDisplay() {
        System.out.println("DEBUG: Updating metrics display...");
        // Metrics are updated directly in loadBusinessMetrics method
    }

    /**
     * Update charts display
     */
    private void updateChartsDisplay() {
        System.out.println("DEBUG: Updating charts display...");
        // Chart updates will be implemented in future iterations
    }

    /**
     * Refresh dashboard data
     */
    private void refreshDashboard() {
        System.out.println("DEBUG: Refreshing dashboard...");
        loadDashboardDataAsync();
    }

    // Navigation methods for quick action buttons
    private void showInventoryManagement() {
        try {
            System.out.println("DEBUG: Navigating to Inventory Management...");
            NavigationUtil.navigateTo(btnInventoryManagement, "InventoryManagement.fxml", "Inventory Management");
        } catch (Exception e) {
            System.err.println("ERROR: Failed to navigate to Inventory Management: " + e.getMessage());
            AlertUtil.showError("Navigation Error", "Failed to navigate to Inventory Management: " + e.getMessage());
        }
    }

    private void showOutstandingBalances() {
        System.out.println("DEBUG: Navigating to Outstanding Balances...");
        try {
            NavigationUtil.navigateTo(btnOutstandingBalances, "OutstandingBalances.fxml", "Outstanding Balances");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Outstanding Balances: " + e.getMessage());
        }
    }

    private void showReports() {
        System.out.println("DEBUG: Navigating to Reports...");
        AlertUtil.showInfo("Navigation", "Reports feature coming soon!");
    }

    private void showSettings() {
        System.out.println("DEBUG: Navigating to Settings...");
        NavigationUtil.navigateToSettings(btnSettings);
    }

    /**
     * Get main container for external access
     */
    public VBox getMainContainer() {
        return mainContainer;
    }
}
