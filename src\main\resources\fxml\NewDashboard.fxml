<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<ScrollPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.NewDashboardController" fitToWidth="true" styleClass="dashboard-scroll">
   <content>
      <VBox spacing="20.0" styleClass="dashboard-container">
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         <children>
            <!-- Header Section -->
            <VBox spacing="10.0" styleClass="dashboard-header">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="20.0">
                     <children>
                        <VBox spacing="5.0">
                           <children>
                              <Label fx:id="lblWelcomeMessage" styleClass="dashboard-title" text="Good Morning, User!">
                                 <font>
                                    <Font name="System Bold" size="28.0" />
                                 </font>
                              </Label>
                              <Label fx:id="lblCurrentDateTime" styleClass="dashboard-subtitle" text="Current Date and Time" />
                           </children>
                        </VBox>
                        <Region HBox.hgrow="ALWAYS" />
                        <VBox alignment="CENTER_RIGHT" spacing="5.0">
                           <children>
                              <Label fx:id="lblSystemStatus" text="🟢 System Online" />
                              <Label fx:id="lblUserRole" text="Role: Manager" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  <Label fx:id="lblSeasonalMessage" styleClass="seasonal-message" text="🌸 Spring Collection - Fresh styles arriving!" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
               </padding>
            </VBox>

            <!-- Quick Stats Cards Row - Created Programmatically -->
            <HBox fx:id="quickStatsContainer" spacing="15.0" styleClass="stats-row">
            </HBox>

            <!-- Performance Metrics Row - Created Programmatically -->
            <HBox fx:id="performanceMetricsContainer" spacing="15.0" styleClass="stats-row">
            </HBox>

            <!-- Financial Metrics Row -->
            <HBox spacing="15.0" styleClass="stats-row">
               <children>
                  <VBox styleClass="metric-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="metric-label" text="Outstanding Balance" />
                        <Label fx:id="lblOutstandingBalance" styleClass="metric-value" text="$0.00" />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="metric-label" text="Pending Refunds" />
                        <Label fx:id="lblPendingRefunds" styleClass="metric-value" text="0" />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="metric-label" text="Top Product" />
                        <Label fx:id="lblTopProduct" styleClass="metric-value" text="Loading..." />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="metric-label" text="Inventory Turnover" />
                        <Label fx:id="lblInventoryTurnover" styleClass="metric-value" text="Calculating..." />
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Conditional Sections -->
            <!-- Manager Section -->
            <VBox fx:id="vboxManagerSection" spacing="15.0" styleClass="conditional-section">
               <children>
                  <Label styleClass="section-title" text="📊 Manager Dashboard" />
                  <HBox spacing="10.0">
                     <children>
                        <Button fx:id="btnViewReports" onAction="#handleViewReports" styleClass="action-button" text="📈 View Reports" />
                        <Button styleClass="action-button" text="👥 Staff Management" />
                        <Button styleClass="action-button" text="💰 Financial Overview" />
                        <Button styleClass="action-button" text="📋 Inventory Analysis" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Cashier Section -->
            <VBox fx:id="vboxCashierSection" spacing="15.0" styleClass="conditional-section">
               <children>
                  <Label styleClass="section-title" text="🛒 Cashier Quick Actions" />
                  <HBox spacing="10.0">
                     <children>
                        <Button fx:id="btnQuickSale" onAction="#handleQuickSale" styleClass="action-button primary-button" text="💳 New Sale" />
                        <Button styleClass="action-button" text="🔍 Product Lookup" />
                        <Button styleClass="action-button" text="👤 Customer Lookup" />
                        <Button styleClass="action-button" text="💸 Process Return" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Weekend Special Section -->
            <VBox fx:id="vboxWeekendSpecial" spacing="15.0" styleClass="conditional-section weekend-special">
               <children>
                  <Label styleClass="section-title" text="🎉 Weekend Special Promotions" />
                  <HBox spacing="10.0">
                     <children>
                        <Label styleClass="promo-text" text="🏷️ Weekend Sale: 20% off selected items" />
                        <Button styleClass="action-button promo-button" text="View Sale Items" />
                        <Button styleClass="action-button promo-button" text="Apply Discount" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Seasonal Promotion Section -->
            <VBox fx:id="vboxSeasonalPromo" spacing="15.0" styleClass="conditional-section seasonal-promo">
               <children>
                  <Label styleClass="section-title" text="🌟 Seasonal Promotions" />
                  <HBox spacing="10.0">
                     <children>
                        <Label styleClass="promo-text" text="New season arrivals - Featured collections available now!" />
                        <Button styleClass="action-button promo-button" text="View Collection" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Data Tables Section -->
            <HBox spacing="20.0" styleClass="tables-section">
               <children>
                  <!-- Recent Transactions -->
                  <VBox styleClass="table-container" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="table-title" text="📋 Recent Transactions (Last 50)" />
                        <TableView fx:id="tblRecentTransactions" styleClass="data-table">
                           <columns>
                              <TableColumn fx:id="colTransactionNumber" prefWidth="120.0" text="Transaction #" />
                              <TableColumn fx:id="colTransactionDate" prefWidth="140.0" text="Date" />
                              <TableColumn fx:id="colCustomer" prefWidth="150.0" text="Customer" />
                              <TableColumn fx:id="colAmount" prefWidth="100.0" text="Amount" />
                              <TableColumn fx:id="colStatus" prefWidth="100.0" text="Status" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>

                  <!-- Top Products -->
                  <VBox styleClass="table-container" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="table-title" text="🏆 Top Products" />
                        <TableView fx:id="tblTopProducts" styleClass="data-table">
                           <columns>
                              <TableColumn fx:id="colProductName" prefWidth="200.0" text="Product Name" />
                              <TableColumn fx:id="colProductSku" prefWidth="100.0" text="SKU" />
                              <TableColumn fx:id="colQuantitySold" prefWidth="80.0" text="Qty Sold" />
                              <TableColumn fx:id="colRevenue" prefWidth="100.0" text="Revenue" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Second Row of Tables -->
            <HBox spacing="20.0" styleClass="tables-section">
               <children>
                  <!-- Recent Customers -->
                  <VBox styleClass="table-container" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="table-title" text="👥 Recent Customers" />
                        <TableView fx:id="tblRecentCustomers" styleClass="data-table">
                           <columns>
                              <TableColumn fx:id="colCustomerName" prefWidth="150.0" text="Customer Name" />
                              <TableColumn fx:id="colCustomerEmail" prefWidth="150.0" text="Phone" />
                              <TableColumn fx:id="colJoinDate" prefWidth="120.0" text="Join Date" />
                              <TableColumn fx:id="colLoyaltyPoints" prefWidth="100.0" text="Loyalty Points" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>

                  <!-- Alerts and Warnings -->
                  <VBox styleClass="table-container alerts-container" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="table-title" text="⚠️ Alerts &amp; Warnings" />
                        <VBox spacing="10.0">
                           <children>
                              <Label styleClass="alert-section-title" text="Low Stock Alerts:" />
                              <VBox fx:id="vboxLowStockAlerts" spacing="5.0" styleClass="alert-list" />
                              <Label styleClass="alert-section-title" text="System Alerts:" />
                              <VBox fx:id="vboxSystemAlerts" spacing="5.0" styleClass="alert-list" />
                              <Label styleClass="alert-section-title" text="Outstanding Balances:" />
                              <VBox fx:id="vboxOutstandingBalances" spacing="5.0" styleClass="alert-list" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Action Buttons Section -->
            <HBox spacing="15.0" styleClass="action-section">
               <children>
                  <Button fx:id="btnAddProduct" onAction="#handleAddProduct" styleClass="action-button primary-button" text="➕ Add Product" />
                  <Button fx:id="btnManageCustomers" onAction="#handleManageCustomers" styleClass="action-button" text="👥 Manage Customers" />
                  <Button fx:id="btnRefreshData" onAction="#handleRefreshData" styleClass="action-button refresh-button" text="🔄 Refresh Data" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label styleClass="last-updated" text="Last updated: Real-time" />
               </children>
            </HBox>
         </children>
      </VBox>
   </content>
</ScrollPane>
