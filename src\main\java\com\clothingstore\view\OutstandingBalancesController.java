package com.clothingstore.view;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.service.RefundService;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.AlertUtil;

import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.LineChart;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.XYChart;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TitledPane;
import javafx.scene.control.Tooltip;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;

/**
 * Enhanced Controller for Outstanding Balances management with programmatic UI
 * creation Following JavaFX 17.0.2 compatibility patterns and dashboard
 * integration
 */
public class OutstandingBalancesController implements Initializable {

    // Main container from FXML (minimal FXML approach)
    @FXML
    private VBox mainContainer;

    // Programmatically created UI components
    private VBox headerSection;
    private HBox metricsCardsContainer;
    private VBox filtersSection;
    private HBox quickFiltersContainer;
    private VBox tableSection;
    private VBox chartsSection;
    private HBox actionButtonsContainer;

    // Filter components
    private ComboBox<String> cmbCustomerFilter;
    private ComboBox<String> cmbStatusFilter;
    private ComboBox<String> cmbDateRange;
    private ComboBox<String> cmbAmountRange;
    private TextField txtSearch;
    private DatePicker dpFromDate;
    private DatePicker dpToDate;

    // Action buttons
    private Button btnRefresh;
    private Button btnClearFilter;
    private Button btnExport;
    private Button btnHighPriority;
    private Button btnOverdue;
    private Button btnInstallments;
    private Button btnRecentTransactions;
    private Button btnDashboardIntegration;

    // Status and summary labels
    private Label lblSummary;
    private Label lblFilteredCount;
    private Label lblTotalOutstanding;
    private Label lblAverageBalance;
    private Label lblOldestTransaction;
    private Label lblPaymentTrends;

    // Table components
    private TableView<Transaction> tblOutstandingBalances;
    private TableColumn<Transaction, String> colTransactionNumber;
    private TableColumn<Transaction, String> colDate;
    private TableColumn<Transaction, String> colCustomer;
    private TableColumn<Transaction, String> colTotalAmount;
    private TableColumn<Transaction, String> colAmountPaid;
    private TableColumn<Transaction, String> colRemainingBalance;
    private TableColumn<Transaction, String> colStatus;
    private TableColumn<Transaction, String> colActions;

    // Selection and action components
    private Label lblSelectedTransaction;
    private Button btnMakePayment;
    private Button btnViewDetails;

    // Dashboard integration components
    private ProgressIndicator loadingIndicator;
    private VBox dashboardMetricsContainer;
    private LineChart<String, Number> trendsChart;
    private PieChart distributionChart;
    private BarChart<String, Number> agingChart;

    // Service dependencies
    private TransactionDAO transactionDAO;
    private CustomerDAO customerDAO;
    private TransactionService transactionService;
    private PaymentHistoryService paymentHistoryService;
    private RefundService refundService;

    // Data collections
    private ObservableList<Transaction> allOutstandingTransactions;
    private ObservableList<Transaction> filteredTransactions;

    // Enhanced data structures for dashboard integration
    private Map<String, BigDecimal> customerBalanceMap;
    private Map<String, Integer> statusCountMap;
    private List<Map<String, Object>> historicalMetrics;
    private Map<String, Object> currentSummary;

    // Caching and performance
    private long lastDataRefresh = 0;
    private static final long CACHE_DURATION_MS = 30000; // 30 seconds
    private CompletableFuture<Void> currentLoadTask;

    // Formatting and constants
    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
    private final DateTimeFormatter timestampFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

    // Dashboard integration
    private boolean isDashboardMode = false;
    private Node dashboardParent;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            // Initialize services
            initializeServices();

            // Initialize data structures
            initializeDataStructures();

            // Create UI programmatically (JavaFX 17.0.2 compatibility)
            createProgrammaticUI();

            // Setup components
            setupTableColumns();
            setupFilters();
            setupEventHandlers();
            setupDashboardIntegration();

            // Load initial data
            loadOutstandingBalancesAsync();

        } catch (Exception e) {
            AlertUtil.showError("Initialization Error", "Failed to initialize Outstanding Balances: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Initialize all service dependencies
     */
    private void initializeServices() {
        transactionDAO = TransactionDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionService = TransactionService.getInstance();
        paymentHistoryService = PaymentHistoryService.getInstance();
        refundService = RefundService.getInstance();
    }

    /**
     * Initialize data structures and collections
     */
    private void initializeDataStructures() {
        allOutstandingTransactions = FXCollections.observableArrayList();
        filteredTransactions = FXCollections.observableArrayList();
        customerBalanceMap = new HashMap<>();
        statusCountMap = new HashMap<>();
        historicalMetrics = new ArrayList<>();
        currentSummary = new HashMap<>();
    }

    /**
     * Create UI components programmatically following JavaFX 17.0.2 patterns
     */
    private void createProgrammaticUI() {
        if (mainContainer == null) {
            mainContainer = new VBox();
            mainContainer.setSpacing(10.0);
            mainContainer.setPadding(new Insets(10));
        }

        // Clear any existing content
        mainContainer.getChildren().clear();

        // Create main sections
        createHeaderSection();
        createMetricsCardsSection();
        createFiltersSection();
        createTableSection();
        createChartsSection();
        createActionButtonsSection();

        // Add all sections to main container
        mainContainer.getChildren().addAll(
                headerSection,
                metricsCardsContainer,
                filtersSection,
                tableSection,
                chartsSection,
                actionButtonsContainer
        );
    }

    /**
     * Create header section with title and loading indicator
     */
    private void createHeaderSection() {
        headerSection = new VBox();
        headerSection.setSpacing(10.0);
        headerSection.getStyleClass().add("header-section");

        // Title
        Label titleLabel = new Label("Outstanding Balances Management");
        titleLabel.getStyleClass().addAll("page-title", "h1");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Loading indicator
        loadingIndicator = new ProgressIndicator();
        loadingIndicator.setVisible(false);
        loadingIndicator.setPrefSize(30, 30);

        HBox titleContainer = new HBox();
        titleContainer.setSpacing(15.0);
        titleContainer.setAlignment(Pos.CENTER_LEFT);
        titleContainer.getChildren().addAll(titleLabel, loadingIndicator);

        headerSection.getChildren().add(titleContainer);
    }

    /**
     * Create metrics cards section for dashboard-style display
     */
    private void createMetricsCardsSection() {
        metricsCardsContainer = new HBox();
        metricsCardsContainer.setSpacing(15.0);
        metricsCardsContainer.getStyleClass().add("metrics-row");

        // Total Outstanding card
        VBox totalCard = createMetricCard("Total Outstanding", "$0.00", "metric-card");
        lblTotalOutstanding = (Label) totalCard.getChildren().get(1);

        // Average Balance card
        VBox avgCard = createMetricCard("Average Balance", "$0.00", "metric-card");
        lblAverageBalance = (Label) avgCard.getChildren().get(1);

        // Oldest Transaction card
        VBox oldestCard = createMetricCard("Oldest Transaction", "N/A", "metric-card", "alert-card");
        lblOldestTransaction = (Label) oldestCard.getChildren().get(1);
        lblOldestTransaction.getStyleClass().add("alert-value");

        // Payment Trends card
        VBox trendsCard = createMetricCard("Payment Trends", "Loading...", "metric-card");
        lblPaymentTrends = (Label) trendsCard.getChildren().get(1);

        metricsCardsContainer.getChildren().addAll(totalCard, avgCard, oldestCard, trendsCard);

        // Set grow properties
        HBox.setHgrow(totalCard, Priority.ALWAYS);
        HBox.setHgrow(avgCard, Priority.ALWAYS);
        HBox.setHgrow(oldestCard, Priority.ALWAYS);
        HBox.setHgrow(trendsCard, Priority.ALWAYS);
    }

    /**
     * Create a metric card following dashboard patterns
     */
    private VBox createMetricCard(String title, String value, String... styleClasses) {
        VBox card = new VBox();
        card.setSpacing(5.0);
        card.setPadding(new Insets(15));
        card.getStyleClass().addAll(styleClasses);
        card.setStyle("-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("metric-title");
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666666; -fx-font-weight: normal;");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("metric-value");
        valueLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * Create filters section with enhanced search and filtering capabilities
     */
    private void createFiltersSection() {
        filtersSection = new VBox();
        filtersSection.setSpacing(10.0);
        filtersSection.getStyleClass().add("filters-section");

        // Filters title
        Label filtersTitle = new Label("Filters & Search");
        filtersTitle.getStyleClass().add("section-title");
        filtersTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #34495e;");

        // Search box
        HBox searchContainer = new HBox();
        searchContainer.setSpacing(10.0);
        searchContainer.setAlignment(Pos.CENTER_LEFT);

        txtSearch = new TextField();
        txtSearch.setPromptText("Search transactions, customers, amounts...");
        txtSearch.setPrefWidth(300);
        txtSearch.getStyleClass().add("search-field");

        // Filter dropdowns
        HBox filtersContainer = new HBox();
        filtersContainer.setSpacing(15.0);
        filtersContainer.setAlignment(Pos.CENTER_LEFT);

        cmbCustomerFilter = new ComboBox<>();
        cmbCustomerFilter.setPromptText("Customer");
        cmbCustomerFilter.setPrefWidth(150);

        cmbStatusFilter = new ComboBox<>();
        cmbStatusFilter.setPromptText("Status");
        cmbStatusFilter.setPrefWidth(120);

        cmbDateRange = new ComboBox<>();
        cmbDateRange.setPromptText("Date Range");
        cmbDateRange.setPrefWidth(120);

        cmbAmountRange = new ComboBox<>();
        cmbAmountRange.setPromptText("Amount Range");
        cmbAmountRange.setPrefWidth(130);

        // Date pickers
        dpFromDate = new DatePicker();
        dpFromDate.setPromptText("From Date");
        dpFromDate.setPrefWidth(120);

        dpToDate = new DatePicker();
        dpToDate.setPromptText("To Date");
        dpToDate.setPrefWidth(120);

        filtersContainer.getChildren().addAll(
                cmbCustomerFilter, cmbStatusFilter, cmbDateRange, cmbAmountRange,
                dpFromDate, dpToDate
        );

        // Quick filter buttons
        createQuickFiltersContainer();

        // Action buttons
        HBox actionButtons = new HBox();
        actionButtons.setSpacing(10.0);
        actionButtons.setAlignment(Pos.CENTER_LEFT);

        btnRefresh = new Button("Refresh");
        btnRefresh.getStyleClass().addAll("btn", "btn-primary");
        btnRefresh.setStyle("-fx-background-color: #3498db; -fx-text-fill: white;");

        btnClearFilter = new Button("Clear Filters");
        btnClearFilter.getStyleClass().addAll("btn", "btn-secondary");
        btnClearFilter.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white;");

        btnExport = new Button("Export");
        btnExport.getStyleClass().addAll("btn", "btn-success");
        btnExport.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white;");

        btnDashboardIntegration = new Button("Dashboard View");
        btnDashboardIntegration.getStyleClass().addAll("btn", "btn-info");
        btnDashboardIntegration.setStyle("-fx-background-color: #e67e22; -fx-text-fill: white;");

        actionButtons.getChildren().addAll(btnRefresh, btnClearFilter, btnExport, btnDashboardIntegration);

        searchContainer.getChildren().addAll(txtSearch, actionButtons);

        filtersSection.getChildren().addAll(
                filtersTitle,
                searchContainer,
                filtersContainer,
                quickFiltersContainer
        );
    }

    /**
     * Create quick filters container
     */
    private void createQuickFiltersContainer() {
        quickFiltersContainer = new HBox();
        quickFiltersContainer.setSpacing(10.0);
        quickFiltersContainer.setAlignment(Pos.CENTER_LEFT);

        Label quickLabel = new Label("Quick Filters:");
        quickLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #7f8c8d;");

        btnHighPriority = new Button("High Priority (>$500)");
        btnHighPriority.getStyleClass().addAll("btn", "btn-outline");
        btnHighPriority.setStyle("-fx-border-color: #e74c3c; -fx-text-fill: #e74c3c;");

        btnOverdue = new Button("Overdue (>90 days)");
        btnOverdue.getStyleClass().addAll("btn", "btn-outline");
        btnOverdue.setStyle("-fx-border-color: #f39c12; -fx-text-fill: #f39c12;");

        btnInstallments = new Button("Installments");
        btnInstallments.getStyleClass().addAll("btn", "btn-outline");
        btnInstallments.setStyle("-fx-border-color: #9b59b6; -fx-text-fill: #9b59b6;");

        btnRecentTransactions = new Button("Recent (30 days)");
        btnRecentTransactions.getStyleClass().addAll("btn", "btn-outline");
        btnRecentTransactions.setStyle("-fx-border-color: #3498db; -fx-text-fill: #3498db;");

        quickFiltersContainer.getChildren().addAll(
                quickLabel, btnHighPriority, btnOverdue, btnInstallments, btnRecentTransactions
        );
    }

    /**
     * Create table section with enhanced table and summary labels
     */
    private void createTableSection() {
        tableSection = new VBox();
        tableSection.setSpacing(10.0);
        tableSection.getStyleClass().add("table-section");

        // Table title and summary
        HBox tableHeader = new HBox();
        tableHeader.setSpacing(15.0);
        tableHeader.setAlignment(Pos.CENTER_LEFT);

        Label tableTitle = new Label("Outstanding Transactions");
        tableTitle.getStyleClass().add("section-title");
        tableTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #34495e;");

        // Summary labels
        lblSummary = new Label("Total Outstanding: $0.00 (0 transactions)");
        lblSummary.getStyleClass().add("summary-label");
        lblSummary.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

        lblFilteredCount = new Label("Showing 0 of 0 transactions");
        lblFilteredCount.getStyleClass().add("filtered-count-label");
        lblFilteredCount.setStyle("-fx-font-size: 12px; -fx-text-fill: #7f8c8d;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        tableHeader.getChildren().addAll(tableTitle, spacer, lblSummary, lblFilteredCount);

        // Create table programmatically
        createOutstandingBalancesTable();

        // Selection info and action buttons
        HBox selectionContainer = new HBox();
        selectionContainer.setSpacing(15.0);
        selectionContainer.setAlignment(Pos.CENTER_LEFT);

        lblSelectedTransaction = new Label("No transaction selected");
        lblSelectedTransaction.setStyle("-fx-text-fill: #7f8c8d; -fx-font-style: italic;");

        btnMakePayment = new Button("Make Payment");
        btnMakePayment.getStyleClass().addAll("btn", "btn-success");
        btnMakePayment.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white;");
        btnMakePayment.setDisable(true);

        btnViewDetails = new Button("View Details");
        btnViewDetails.getStyleClass().addAll("btn", "btn-info");
        btnViewDetails.setStyle("-fx-background-color: #3498db; -fx-text-fill: white;");
        btnViewDetails.setDisable(true);

        Region selectionSpacer = new Region();
        HBox.setHgrow(selectionSpacer, Priority.ALWAYS);

        selectionContainer.getChildren().addAll(
                lblSelectedTransaction, selectionSpacer, btnMakePayment, btnViewDetails
        );

        tableSection.getChildren().addAll(tableHeader, tblOutstandingBalances, selectionContainer);
    }

    /**
     * Create the outstanding balances table programmatically
     */
    private void createOutstandingBalancesTable() {
        tblOutstandingBalances = new TableView<>();
        tblOutstandingBalances.getStyleClass().add("outstanding-balances-table");
        tblOutstandingBalances.setPrefHeight(400);

        // Create columns
        colTransactionNumber = new TableColumn<>("Transaction #");
        colTransactionNumber.setPrefWidth(120);

        colDate = new TableColumn<>("Date");
        colDate.setPrefWidth(100);

        colCustomer = new TableColumn<>("Customer");
        colCustomer.setPrefWidth(150);

        colTotalAmount = new TableColumn<>("Total Amount");
        colTotalAmount.setPrefWidth(120);

        colAmountPaid = new TableColumn<>("Amount Paid");
        colAmountPaid.setPrefWidth(120);

        colRemainingBalance = new TableColumn<>("Remaining Balance");
        colRemainingBalance.setPrefWidth(140);

        colStatus = new TableColumn<>("Status");
        colStatus.setPrefWidth(120);

        colActions = new TableColumn<>("Actions");
        colActions.setPrefWidth(200);

        tblOutstandingBalances.getColumns().addAll(
                colTransactionNumber, colDate, colCustomer, colTotalAmount,
                colAmountPaid, colRemainingBalance, colStatus, colActions
        );
    }

    /**
     * Create charts section for data visualization
     */
    private void createChartsSection() {
        chartsSection = new VBox();
        chartsSection.setSpacing(15.0);
        chartsSection.getStyleClass().add("charts-section");

        // Charts title
        Label chartsTitle = new Label("Outstanding Balances Analytics");
        chartsTitle.getStyleClass().add("section-title");
        chartsTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #34495e;");

        // Charts container
        HBox chartsContainer = new HBox();
        chartsContainer.setSpacing(15.0);

        // Trends chart
        createTrendsChart();

        // Distribution chart
        createDistributionChart();

        // Aging chart
        createAgingChart();

        chartsContainer.getChildren().addAll(trendsChart, distributionChart, agingChart);

        // Make charts collapsible
        TitledPane chartsPane = new TitledPane("Analytics Dashboard", chartsContainer);
        chartsPane.setExpanded(false);
        chartsPane.getStyleClass().add("charts-pane");

        chartsSection.getChildren().addAll(chartsTitle, chartsPane);
    }

    /**
     * Create action buttons section
     */
    private void createActionButtonsSection() {
        actionButtonsContainer = new HBox();
        actionButtonsContainer.setSpacing(15.0);
        actionButtonsContainer.setAlignment(Pos.CENTER);
        actionButtonsContainer.setPadding(new Insets(20, 0, 10, 0));
        actionButtonsContainer.getStyleClass().add("action-buttons-section");

        // Additional action buttons can be added here as needed
        Label actionsLabel = new Label("Additional actions and reports will be available here");
        actionsLabel.setStyle("-fx-text-fill: #95a5a6; -fx-font-style: italic;");

        actionButtonsContainer.getChildren().add(actionsLabel);
    }

    /**
     * Create trends chart for outstanding balances over time
     */
    private void createTrendsChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        trendsChart = new LineChart<>(xAxis, yAxis);
        trendsChart.setTitle("Outstanding Balances Trend");
        trendsChart.setPrefSize(300, 200);
        trendsChart.setLegendVisible(false);

        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Outstanding Amount");
        trendsChart.getData().add(series);
    }

    /**
     * Create distribution chart for outstanding balances by status
     */
    private void createDistributionChart() {
        distributionChart = new PieChart();
        distributionChart.setTitle("Status Distribution");
        distributionChart.setPrefSize(300, 200);
        distributionChart.setLegendVisible(true);
    }

    /**
     * Create aging chart for outstanding balances by age
     */
    private void createAgingChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        agingChart = new BarChart<>(xAxis, yAxis);
        agingChart.setTitle("Aging Analysis");
        agingChart.setPrefSize(300, 200);
        agingChart.setLegendVisible(false);

        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Count");
        agingChart.getData().add(series);
    }

    /**
     * Setup dashboard integration features
     */
    private void setupDashboardIntegration() {
        // Dashboard metrics container
        dashboardMetricsContainer = new VBox();
        dashboardMetricsContainer.setSpacing(10.0);
        dashboardMetricsContainer.getStyleClass().add("dashboard-metrics");

        // Initialize dashboard mode flag
        isDashboardMode = false;
    }

    /**
     * Load outstanding balances data asynchronously with caching
     */
    private void loadOutstandingBalancesAsync() {
        // Check cache first
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastDataRefresh < CACHE_DURATION_MS && !allOutstandingTransactions.isEmpty()) {
            updateDisplayData();
            return;
        }

        // Cancel any existing load task
        if (currentLoadTask != null && !currentLoadTask.isDone()) {
            currentLoadTask.cancel(true);
        }

        // Show loading indicator
        Platform.runLater(() -> {
            if (loadingIndicator != null) {
                loadingIndicator.setVisible(true);
            }
        });

        // Create new load task
        currentLoadTask = CompletableFuture.runAsync(() -> {
            try {
                List<Transaction> transactions = transactionDAO.findWithOutstandingBalances();

                Platform.runLater(() -> {
                    allOutstandingTransactions.clear();
                    allOutstandingTransactions.addAll(transactions);
                    lastDataRefresh = currentTime;
                    updateDisplayData();
                    updateMetricsCards();
                    updateCharts();

                    if (loadingIndicator != null) {
                        loadingIndicator.setVisible(false);
                    }
                });

            } catch (Exception e) {
                Platform.runLater(() -> {
                    AlertUtil.showError("Data Loading Error", "Failed to load outstanding balances: " + e.getMessage());
                    if (loadingIndicator != null) {
                        loadingIndicator.setVisible(false);
                    }
                });
            }
        });
    }

    /**
     * Update display data after loading
     */
    private void updateDisplayData() {
        filteredTransactions.clear();
        filteredTransactions.addAll(allOutstandingTransactions);
        tblOutstandingBalances.setItems(filteredTransactions);
        updateSummary();
        updateFilteredCount();
    }

    /**
     * Update metrics cards with current data
     */
    private void updateMetricsCards() {
        if (allOutstandingTransactions.isEmpty()) {
            return;
        }

        // Calculate metrics
        BigDecimal totalOutstanding = allOutstandingTransactions.stream()
                .map(Transaction::getRemainingBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageBalance = totalOutstanding.divide(
                BigDecimal.valueOf(allOutstandingTransactions.size()), 2, RoundingMode.HALF_UP);

        // Find oldest transaction
        Optional<Transaction> oldest = allOutstandingTransactions.stream()
                .min((t1, t2) -> t1.getTransactionDate().compareTo(t2.getTransactionDate()));

        String oldestDate = oldest.map(t -> dateFormatter.format(t.getTransactionDate())).orElse("N/A");

        // Update labels
        Platform.runLater(() -> {
            if (lblTotalOutstanding != null) {
                lblTotalOutstanding.setText(currencyFormat.format(totalOutstanding));
            }
            if (lblAverageBalance != null) {
                lblAverageBalance.setText(currencyFormat.format(averageBalance));
            }
            if (lblOldestTransaction != null) {
                lblOldestTransaction.setText(oldestDate);
            }
            if (lblPaymentTrends != null) {
                lblPaymentTrends.setText("Updated");
            }
        });
    }

    /**
     * Update charts with current data
     */
    private void updateCharts() {
        updateTrendsChart();
        updateDistributionChart();
        updateAgingChart();
    }

    /**
     * Update trends chart with historical data
     */
    private void updateTrendsChart() {
        if (trendsChart == null) {
            return;
        }

        XYChart.Series<String, Number> series = trendsChart.getData().get(0);
        series.getData().clear();

        // Add sample trend data (in real implementation, this would use historical data)
        series.getData().add(new XYChart.Data<>("Last Week", 15000));
        series.getData().add(new XYChart.Data<>("This Week", 12000));
        series.getData().add(new XYChart.Data<>("Today", 8500));
    }

    /**
     * Update distribution chart with status breakdown
     */
    private void updateDistributionChart() {
        if (distributionChart == null) {
            return;
        }

        distributionChart.getData().clear();

        // Calculate status distribution
        Map<String, Integer> statusCounts = allOutstandingTransactions.stream()
                .collect(Collectors.groupingBy(
                        Transaction::getStatus,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));

        statusCounts.forEach((status, count) -> {
            PieChart.Data data = new PieChart.Data(status + " (" + count + ")", count);
            distributionChart.getData().add(data);
        });
    }

    /**
     * Update aging chart with age analysis
     */
    private void updateAgingChart() {
        if (agingChart == null) {
            return;
        }

        XYChart.Series<String, Number> series = agingChart.getData().get(0);
        series.getData().clear();

        // Calculate aging buckets
        LocalDate now = LocalDate.now();
        Map<String, Long> agingCounts = allOutstandingTransactions.stream()
                .collect(Collectors.groupingBy(t -> {
                    long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(t.getTransactionDate(), now);
                    if (daysBetween <= 30) {
                        return "0-30 days";
                    } else if (daysBetween <= 60) {
                        return "31-60 days";
                    } else if (daysBetween <= 90) {
                        return "61-90 days";
                    } else {
                        return "90+ days";
                    }
                }, Collectors.counting()));

        agingCounts.forEach((bucket, count) -> {
            series.getData().add(new XYChart.Data<>(bucket, count));
        });
    }

    /**
     * Get dashboard metrics for integration with main dashboard
     */
    public Map<String, Object> getDashboardMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        if (allOutstandingTransactions.isEmpty()) {
            metrics.put("totalOutstanding", BigDecimal.ZERO);
            metrics.put("transactionCount", 0);
            metrics.put("averageBalance", BigDecimal.ZERO);
            metrics.put("oldestTransactionDays", 0);
            return metrics;
        }

        BigDecimal totalOutstanding = allOutstandingTransactions.stream()
                .map(Transaction::getRemainingBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageBalance = totalOutstanding.divide(
                BigDecimal.valueOf(allOutstandingTransactions.size()), 2, RoundingMode.HALF_UP);

        Optional<Transaction> oldest = allOutstandingTransactions.stream()
                .min((t1, t2) -> t1.getTransactionDate().compareTo(t2.getTransactionDate()));

        long oldestDays = oldest.map(t
                -> java.time.temporal.ChronoUnit.DAYS.between(t.getTransactionDate(), LocalDate.now())
        ).orElse(0L);

        metrics.put("totalOutstanding", totalOutstanding);
        metrics.put("transactionCount", allOutstandingTransactions.size());
        metrics.put("averageBalance", averageBalance);
        metrics.put("oldestTransactionDays", oldestDays);
        metrics.put("statusBreakdown", getStatusBreakdown());

        return metrics;
    }

    /**
     * Get status breakdown for dashboard integration
     */
    private Map<String, Integer> getStatusBreakdown() {
        return allOutstandingTransactions.stream()
                .collect(Collectors.groupingBy(
                        Transaction::getStatus,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    /**
     * Enable dashboard mode for integration
     */
    public void enableDashboardMode(Node parent) {
        isDashboardMode = true;
        dashboardParent = parent;

        // Hide some UI elements in dashboard mode
        if (chartsSection != null) {
            chartsSection.setVisible(false);
        }
        if (actionButtonsContainer != null) {
            actionButtonsContainer.setVisible(false);
        }
    }

    /**
     * Disable dashboard mode
     */
    public void disableDashboardMode() {
        isDashboardMode = false;
        dashboardParent = null;

        // Show all UI elements
        if (chartsSection != null) {
            chartsSection.setVisible(true);
        }
        if (actionButtonsContainer != null) {
            actionButtonsContainer.setVisible(true);
        }
    }

    private void setupTableColumns() {
        // Transaction Number
        colTransactionNumber.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));

        // Date
        colDate.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getTransactionDate().format(dateFormatter)));

        // Customer
        colCustomer.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String customerName = transaction.getCustomerName();
            return new SimpleStringProperty(customerName != null ? customerName : "Walk-in Customer");
        });

        // Total Amount
        colTotalAmount.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotalAmount())));

        // Amount Paid
        colAmountPaid.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getAmountPaid())));

        // Remaining Balance
        colRemainingBalance.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getRemainingBalance())));

        // Status
        colStatus.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String status = transaction.getStatus();

            // Check if this is an installment transaction
            if (transaction.isInstallmentTransaction()) {
                String installmentStatus = transaction.getInstallmentStatus();
                int completed = transaction.getCompletedInstallments();
                BigDecimal amountPaid = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO;
                BigDecimal totalAmount = transaction.getTotalAmount();

                // Calculate progress percentage
                double progressPercent = 0.0;
                if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    progressPercent = amountPaid.divide(totalAmount, 4, java.math.RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).doubleValue();
                }

                if ("PENDING".equals(installmentStatus)) {
                    return new SimpleStringProperty("Installment - Pending First Payment");
                } else if ("IN_PROGRESS".equals(installmentStatus)) {
                    return new SimpleStringProperty(String.format("Installment - %d payment(s) made (%.1f%% complete)",
                            completed, progressPercent));
                } else if ("COMPLETED".equals(installmentStatus)) {
                    return new SimpleStringProperty("Installment - Completed (100%)");
                } else {
                    return new SimpleStringProperty("Installment - " + installmentStatus);
                }
            }

            // Regular transaction statuses
            // Removed partial payment status - only support full transactions
            if ("PENDING".equals(status)) {
                return new SimpleStringProperty("Pending");
            } else if ("PENDING_COMPLETION".equals(status)) {
                return new SimpleStringProperty("Ready for Completion");
            } else if ("COMPLETED".equals(status)) {
                return new SimpleStringProperty("Completed");
            } else if ("REFUNDED".equals(status)) {
                return new SimpleStringProperty("Refunded");
            }
            return new SimpleStringProperty(status);
        });

        // Actions
        colActions.setCellFactory(col -> new TableCell<Transaction, String>() {
            private final Button paymentBtn = new Button("Make Payment");
            private final Button detailsBtn = new Button("Details");
            private final Button refundBtn = new Button("Refund");

            {
                paymentBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 10px;");
                detailsBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");
                refundBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 10px;");

                paymentBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleMakePaymentForTransaction(transaction);
                });

                detailsBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleViewDetailsForTransaction(transaction);
                });

                refundBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleRefundForTransaction(transaction);
                });
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    Transaction transaction = getTableView().getItems().get(getIndex());

                    // Show refund button only if transaction has payments
                    boolean hasPayments = false;
                    try {
                        BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
                        hasPayments = totalPaid.compareTo(BigDecimal.ZERO) > 0;
                    } catch (Exception ex) {
                        // If error checking payments, hide refund button
                        hasPayments = false;
                    }

                    HBox buttons;
                    if (hasPayments) {
                        buttons = new HBox(3, paymentBtn, detailsBtn, refundBtn);
                    } else {
                        buttons = new HBox(5, paymentBtn, detailsBtn);
                    }
                    setGraphic(buttons);
                }
            }
        });

        tblOutstandingBalances.setItems(filteredTransactions);
    }

    private void setupFilters() {
        // Status filter (removed partial payment option)
        cmbStatusFilter.setItems(FXCollections.observableArrayList(
                "All Statuses", "Pending", "Installment Transactions", "Regular Transactions", "Completed", "Refunded"
        ));
        cmbStatusFilter.setValue("All Statuses");

        // Date range filter
        cmbDateRange.setItems(FXCollections.observableArrayList(
                "All Dates", "Last 7 days", "Last 30 days", "Last 90 days", "Last 6 months", "Custom Range"
        ));
        cmbDateRange.setValue("All Dates");

        // Amount range filter
        cmbAmountRange.setItems(FXCollections.observableArrayList(
                "All Amounts", "Under $100", "$100 - $500", "$500 - $1,000", "$1,000 - $5,000", "Over $5,000"
        ));
        cmbAmountRange.setValue("All Amounts");

        // Customer filter will be populated when data is loaded
        cmbCustomerFilter.setValue("All Customers");

        // Setup date pickers
        dpFromDate.setDisable(true);
        dpToDate.setDisable(true);

        // Add listeners for filtering
        cmbCustomerFilter.setOnAction(e -> applyFilters());
        cmbStatusFilter.setOnAction(e -> applyFilters());
        cmbDateRange.setOnAction(e -> {
            boolean isCustomRange = "Custom Range".equals(cmbDateRange.getValue());
            dpFromDate.setDisable(!isCustomRange);
            dpToDate.setDisable(!isCustomRange);
            applyFilters();
        });
        cmbAmountRange.setOnAction(e -> applyFilters());

        // Search field listener with debouncing
        txtSearch.textProperty().addListener((obs, oldVal, newVal) -> {
            // Simple debouncing - apply filter after short delay
            javafx.application.Platform.runLater(() -> applyFilters());
        });

        dpFromDate.setOnAction(e -> applyFilters());
        dpToDate.setOnAction(e -> applyFilters());
    }

    private void setupEventHandlers() {
        // Table selection
        tblOutstandingBalances.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            boolean hasSelection = newSelection != null;
            btnMakePayment.setDisable(!hasSelection);
            btnViewDetails.setDisable(!hasSelection);

            if (hasSelection) {
                lblSelectedTransaction.setText(newSelection.getTransactionNumber()
                        + " - " + currencyFormat.format(newSelection.getRemainingBalance()) + " remaining");
            } else {
                lblSelectedTransaction.setText("None");
            }
        });

        // Add double-click handler for installment tracking
        tblOutstandingBalances.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                Transaction selectedTransaction = tblOutstandingBalances.getSelectionModel().getSelectedItem();
                if (selectedTransaction != null && selectedTransaction.isInstallmentTransaction()) {
                    showInstallmentTrackingDetails(selectedTransaction);
                } else if (selectedTransaction != null) {
                    handleViewDetailsForTransaction(selectedTransaction);
                }
            }
        });
    }

    @FXML
    private void handleRefresh() {
        loadOutstandingBalances();
    }

    @FXML
    private void handleClearFilter() {
        clearAllFilters();
        applyFilters();
    }

    @FXML
    private void handleMakePayment() {
        Transaction selected = tblOutstandingBalances.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleMakePaymentForTransaction(selected);
        }
    }

    @FXML
    private void handleViewDetails() {
        Transaction selected = tblOutstandingBalances.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewDetailsForTransaction(selected);
        }
    }

    private void loadOutstandingBalances() {
        // Show loading indicator
        if (javafx.application.Platform.isFxApplicationThread()) {
            tblOutstandingBalances.setPlaceholder(new javafx.scene.control.Label("Loading outstanding balances..."));
        }

        // Load data in background thread
        javafx.concurrent.Task<List<Transaction>> loadTask = new javafx.concurrent.Task<List<Transaction>>() {
            @Override
            protected List<Transaction> call() throws Exception {
                return transactionDAO.findWithOutstandingBalances();
            }

            @Override
            protected void succeeded() {
                javafx.application.Platform.runLater(() -> {
                    try {
                        List<Transaction> transactions = getValue();
                        allOutstandingTransactions.setAll(transactions);

                        // Update customer filter
                        List<String> customers = transactions.stream()
                                .map(t -> t.getCustomerName() != null ? t.getCustomerName() : "Walk-in Customer")
                                .distinct()
                                .sorted()
                                .collect(Collectors.toList());
                        customers.add(0, "All Customers");
                        cmbCustomerFilter.setItems(FXCollections.observableArrayList(customers));
                        cmbCustomerFilter.setValue("All Customers");

                        applyFilters();
                        tblOutstandingBalances.setPlaceholder(new javafx.scene.control.Label("No outstanding balances found"));
                    } catch (Exception e) {
                        failed();
                    }
                });
            }

            @Override
            protected void failed() {
                javafx.application.Platform.runLater(() -> {
                    AlertUtil.showError("Database Error", "Failed to load outstanding balances: " + getException().getMessage());
                    tblOutstandingBalances.setPlaceholder(new javafx.scene.control.Label("Error loading outstanding balances"));
                });
            }
        };

        // Run task in background thread
        Thread loadThread = new Thread(loadTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }

    private void applyFilters() {
        String customerFilter = cmbCustomerFilter.getValue();
        String statusFilter = cmbStatusFilter.getValue();
        String dateRangeFilter = cmbDateRange.getValue();
        String amountRangeFilter = cmbAmountRange.getValue();
        String searchText = txtSearch.getText();

        List<Transaction> filtered = allOutstandingTransactions.stream()
                .filter(t -> matchesCustomerFilter(t, customerFilter))
                .filter(t -> matchesStatusFilter(t, statusFilter))
                .filter(t -> matchesDateRangeFilter(t, dateRangeFilter))
                .filter(t -> matchesAmountRangeFilter(t, amountRangeFilter))
                .filter(t -> matchesSearchFilter(t, searchText))
                .collect(Collectors.toList());

        filteredTransactions.setAll(filtered);
        updateSummary();
        updateFilteredCount();
    }

    private boolean matchesCustomerFilter(Transaction transaction, String filter) {
        if (filter == null || "All Customers".equals(filter)) {
            return true;
        }

        String customerName = transaction.getCustomerName();
        if (customerName == null) {
            customerName = "Walk-in Customer";
        }

        return filter.equals(customerName);
    }

    private boolean matchesStatusFilter(Transaction transaction, String filter) {
        if (filter == null || "All Statuses".equals(filter)) {
            return true;
        }

        String status = transaction.getStatus();

        if ("Pending".equals(filter)) {
            return "PENDING".equals(status);
        } else if ("Installment Transactions".equals(filter)) {
            return transaction.isInstallmentTransaction();
        } else if ("Regular Transactions".equals(filter)) {
            return !transaction.isInstallmentTransaction();
        } else if ("Completed".equals(filter)) {
            return "COMPLETED".equals(status);
        } else if ("Refunded".equals(filter)) {
            return "REFUNDED".equals(status);
        }

        return false;
    }

    private boolean matchesDateRangeFilter(Transaction transaction, String filter) {
        if (filter == null || "All Dates".equals(filter)) {
            return true;
        }

        LocalDateTime transactionDate = transaction.getTransactionDate();
        LocalDateTime now = LocalDateTime.now();

        switch (filter) {
            case "Last 7 days":
                return transactionDate.isAfter(now.minusDays(7));
            case "Last 30 days":
                return transactionDate.isAfter(now.minusDays(30));
            case "Last 90 days":
                return transactionDate.isAfter(now.minusDays(90));
            case "Last 6 months":
                return transactionDate.isAfter(now.minusMonths(6));
            case "Custom Range":
                return matchesCustomDateRange(transaction);
            default:
                return true;
        }
    }

    private boolean matchesCustomDateRange(Transaction transaction) {
        LocalDate fromDate = dpFromDate.getValue();
        LocalDate toDate = dpToDate.getValue();

        if (fromDate == null && toDate == null) {
            return true;
        }

        LocalDate transactionDate = transaction.getTransactionDate().toLocalDate();

        if (fromDate != null && transactionDate.isBefore(fromDate)) {
            return false;
        }

        if (toDate != null && transactionDate.isAfter(toDate)) {
            return false;
        }

        return true;
    }

    private boolean matchesAmountRangeFilter(Transaction transaction, String filter) {
        if (filter == null || "All Amounts".equals(filter)) {
            return true;
        }

        BigDecimal remainingBalance = transaction.getRemainingBalance();

        switch (filter) {
            case "Under $100":
                return remainingBalance.compareTo(new BigDecimal("100")) < 0;
            case "$100 - $500":
                return remainingBalance.compareTo(new BigDecimal("100")) >= 0
                        && remainingBalance.compareTo(new BigDecimal("500")) <= 0;
            case "$500 - $1,000":
                return remainingBalance.compareTo(new BigDecimal("500")) >= 0
                        && remainingBalance.compareTo(new BigDecimal("1000")) <= 0;
            case "$1,000 - $5,000":
                return remainingBalance.compareTo(new BigDecimal("1000")) >= 0
                        && remainingBalance.compareTo(new BigDecimal("5000")) <= 0;
            case "Over $5,000":
                return remainingBalance.compareTo(new BigDecimal("5000")) > 0;
            default:
                return true;
        }
    }

    private boolean matchesSearchFilter(Transaction transaction, String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return true;
        }

        String search = searchText.toLowerCase().trim();

        // Search in transaction number
        if (transaction.getTransactionNumber() != null
                && transaction.getTransactionNumber().toLowerCase().contains(search)) {
            return true;
        }

        // Search in customer name
        String customerName = transaction.getCustomerName();
        if (customerName == null) {
            customerName = "Walk-in Customer";
        }
        if (customerName.toLowerCase().contains(search)) {
            return true;
        }

        return false;
    }

    private void updateSummary() {
        BigDecimal totalOutstanding = filteredTransactions.stream()
                .map(Transaction::getRemainingBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        lblSummary.setText("Total Outstanding: " + currencyFormat.format(totalOutstanding)
                + " (" + filteredTransactions.size() + " transactions)");
    }

    private void updateFilteredCount() {
        lblFilteredCount.setText("Showing " + filteredTransactions.size() + " of " + allOutstandingTransactions.size() + " transactions");
    }

    // Quick Filter Button Handlers
    @FXML
    private void handleHighPriorityFilter() {
        clearAllFilters();
        cmbAmountRange.setValue("Over $500");
        applyFilters();
    }

    @FXML
    private void handleOverdueFilter() {
        clearAllFilters();
        cmbDateRange.setValue("Last 90 days");
        applyFilters();
    }

    @FXML
    private void handleInstallmentsFilter() {
        clearAllFilters();
        cmbStatusFilter.setValue("Installment Transactions");
        applyFilters();
    }

    @FXML
    private void handleRecentFilter() {
        clearAllFilters();
        cmbDateRange.setValue("Last 7 days");
        applyFilters();
    }

    @FXML
    private void handleExport() {
        // TODO: Implement export functionality
        AlertUtil.showInfo("Export Feature", "Export functionality will be implemented in a future update.");
    }

    /**
     * Show detailed installment payment tracking for a transaction
     */
    private void showInstallmentTrackingDetails(Transaction transaction) {
        if (!transaction.isInstallmentTransaction()) {
            AlertUtil.showInfo("Not an Installment", "This transaction is not an installment payment.");
            return;
        }

        try {
            // Create a detailed installment tracking dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Installment Payment Tracking - " + transaction.getTransactionNumber());

            javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
            root.setPadding(new javafx.geometry.Insets(20));
            root.setStyle("-fx-background-color: #f8f9fa;");

            // Header information
            javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Installment Payment Details");
            titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            // Transaction summary
            javafx.scene.layout.GridPane summaryGrid = new javafx.scene.layout.GridPane();
            summaryGrid.setHgap(15);
            summaryGrid.setVgap(10);
            summaryGrid.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

            addDetailRow(summaryGrid, 0, "Transaction Number:", transaction.getTransactionNumber());
            addDetailRow(summaryGrid, 1, "Customer:", transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer");
            addDetailRow(summaryGrid, 2, "Transaction Date:", transaction.getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")));
            addDetailRow(summaryGrid, 3, "Total Amount:", NumberFormat.getCurrencyInstance().format(transaction.getTotalAmount()));
            addDetailRow(summaryGrid, 4, "Amount Paid:", NumberFormat.getCurrencyInstance().format(transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO));
            addDetailRow(summaryGrid, 5, "Remaining Balance:", NumberFormat.getCurrencyInstance().format(transaction.getRemainingBalance()));
            addDetailRow(summaryGrid, 6, "Installment Status:", transaction.getInstallmentStatus());
            addDetailRow(summaryGrid, 7, "Completed Payments:", String.valueOf(transaction.getCompletedInstallments()));

            // Progress bar
            javafx.scene.control.ProgressBar progressBar = new javafx.scene.control.ProgressBar();
            BigDecimal totalAmount = transaction.getTotalAmount();
            BigDecimal amountPaid = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO;
            double progress = 0.0;
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                progress = amountPaid.divide(totalAmount, 4, java.math.RoundingMode.HALF_UP).doubleValue();
            }
            progressBar.setProgress(progress);
            progressBar.setPrefWidth(300);
            progressBar.setStyle("-fx-accent: #28a745;");

            javafx.scene.control.Label progressLabel = new javafx.scene.control.Label(String.format("Payment Progress: %.1f%%", progress * 100));
            progressLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");

            javafx.scene.layout.VBox progressBox = new javafx.scene.layout.VBox(5);
            progressBox.getChildren().addAll(progressLabel, progressBar);
            progressBox.setAlignment(javafx.geometry.Pos.CENTER);

            // Payment history table
            javafx.scene.control.Label historyLabel = new javafx.scene.control.Label("Payment History");
            historyLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            javafx.scene.control.TableView<com.clothingstore.model.InstallmentPayment> paymentTable = createInstallmentPaymentTable(transaction.getId());

            // Close button
            javafx.scene.control.Button closeButton = new javafx.scene.control.Button("Close");
            closeButton.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20;");
            closeButton.setOnAction(e -> stage.close());

            javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox();
            buttonBox.setAlignment(javafx.geometry.Pos.CENTER);
            buttonBox.getChildren().add(closeButton);

            root.getChildren().addAll(titleLabel, summaryGrid, progressBox, historyLabel, paymentTable, buttonBox);

            javafx.scene.Scene scene = new javafx.scene.Scene(root, 800, 600);
            stage.setScene(scene);
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.setResizable(true);
            stage.show();

        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to show installment tracking details: " + e.getMessage());
        }
    }

    /**
     * Add a detail row to the grid
     */
    /**
     * Create installment payment history table
     */
    private javafx.scene.control.TableView<com.clothingstore.model.InstallmentPayment> createInstallmentPaymentTable(Long transactionId) {
        javafx.scene.control.TableView<com.clothingstore.model.InstallmentPayment> table = new javafx.scene.control.TableView<>();

        // Payment Number column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, Integer> paymentNumCol
                = new javafx.scene.control.TableColumn<>("Payment #");
        paymentNumCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("paymentNumber"));
        paymentNumCol.setPrefWidth(80);

        // Amount column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, String> amountCol
                = new javafx.scene.control.TableColumn<>("Amount");
        amountCol.setCellValueFactory(cellData -> {
            BigDecimal amount = cellData.getValue().getPaymentAmount();
            return new javafx.beans.property.SimpleStringProperty(NumberFormat.getCurrencyInstance().format(amount));
        });
        amountCol.setPrefWidth(100);

        // Payment Method column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, String> methodCol
                = new javafx.scene.control.TableColumn<>("Method");
        methodCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("paymentMethod"));
        methodCol.setPrefWidth(100);

        // Date column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, String> dateCol
                = new javafx.scene.control.TableColumn<>("Date");
        dateCol.setCellValueFactory(cellData -> {
            java.time.LocalDateTime date = cellData.getValue().getPaymentDate();
            String formattedDate = date.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm"));
            return new javafx.beans.property.SimpleStringProperty(formattedDate);
        });
        dateCol.setPrefWidth(150);

        // Status column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, String> statusCol
                = new javafx.scene.control.TableColumn<>("Status");
        statusCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("status"));
        statusCol.setPrefWidth(100);

        // Cashier column
        javafx.scene.control.TableColumn<com.clothingstore.model.InstallmentPayment, String> cashierCol
                = new javafx.scene.control.TableColumn<>("Cashier");
        cashierCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("cashierName"));
        cashierCol.setPrefWidth(120);

        table.getColumns().addAll(paymentNumCol, amountCol, methodCol, dateCol, statusCol, cashierCol);

        // Load payment data
        try {
            com.clothingstore.dao.InstallmentPaymentDAO installmentDAO = com.clothingstore.dao.InstallmentPaymentDAO.getInstance();
            java.util.List<com.clothingstore.model.InstallmentPayment> payments = installmentDAO.findByTransactionId(transactionId);
            table.setItems(javafx.collections.FXCollections.observableArrayList(payments));
        } catch (Exception e) {
            System.err.println("Error loading installment payment data: " + e.getMessage());
        }

        table.setPrefHeight(200);
        return table;
    }

    private void clearAllFilters() {
        cmbCustomerFilter.setValue("All Customers");
        cmbStatusFilter.setValue("All Statuses");
        cmbDateRange.setValue("All Dates");
        cmbAmountRange.setValue("All Amounts");
        txtSearch.clear();
        dpFromDate.setValue(null);
        dpToDate.setValue(null);
        dpFromDate.setDisable(true);
        dpToDate.setDisable(true);
    }

    private void handleMakePaymentForTransaction(Transaction transaction) {
        try {
            System.out.println("DEBUG: Starting payment dialog for transaction: " + transaction.getTransactionNumber());
            System.out.println("DEBUG: Transaction remaining balance: " + transaction.getRemainingBalance());

            // Create programmatic payment dialog to avoid FXML issues
            System.out.println("DEBUG: Creating programmatic payment dialog...");

            // Create a simple payment dialog programmatically
            javafx.scene.control.Dialog<javafx.util.Pair<String, BigDecimal>> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Make Payment - " + transaction.getTransactionNumber());
            dialog.setHeaderText("Process Outstanding Payment");

            // Set the button types
            javafx.scene.control.ButtonType payButtonType = new javafx.scene.control.ButtonType("Process Payment", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(payButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Create the payment form
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            javafx.scene.control.ComboBox<String> paymentMethodCombo = new javafx.scene.control.ComboBox<>();
            paymentMethodCombo.getItems().addAll("Cash", "Credit Card", "Debit Card", "Check", "Bank Transfer");
            paymentMethodCombo.setValue("Cash");

            javafx.scene.control.TextField amountField = new javafx.scene.control.TextField();
            amountField.setPromptText("Enter payment amount");
            amountField.setText(transaction.getRemainingBalance().toString());

            grid.add(new javafx.scene.control.Label("Transaction:"), 0, 0);
            grid.add(new javafx.scene.control.Label(transaction.getTransactionNumber()), 1, 0);
            grid.add(new javafx.scene.control.Label("Total Amount:"), 0, 1);
            grid.add(new javafx.scene.control.Label(NumberFormat.getCurrencyInstance().format(transaction.getTotalAmount())), 1, 1);
            grid.add(new javafx.scene.control.Label("Amount Paid:"), 0, 2);
            grid.add(new javafx.scene.control.Label(NumberFormat.getCurrencyInstance().format(transaction.getAmountPaid())), 1, 2);
            grid.add(new javafx.scene.control.Label("Remaining Balance:"), 0, 3);
            grid.add(new javafx.scene.control.Label(NumberFormat.getCurrencyInstance().format(transaction.getRemainingBalance())), 1, 3);
            grid.add(new javafx.scene.control.Label("Payment Method:"), 0, 4);
            grid.add(paymentMethodCombo, 1, 4);
            grid.add(new javafx.scene.control.Label("Payment Amount:"), 0, 5);
            grid.add(amountField, 1, 5);

            dialog.getDialogPane().setContent(grid);

            // Convert the result when the pay button is clicked
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == payButtonType) {
                    try {
                        BigDecimal amount = new BigDecimal(amountField.getText());
                        return new javafx.util.Pair<>(paymentMethodCombo.getValue(), amount);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            });

            System.out.println("DEBUG: About to show programmatic dialog...");
            java.util.Optional<javafx.util.Pair<String, BigDecimal>> result = dialog.showAndWait();
            System.out.println("DEBUG: Dialog closed");

            boolean paymentProcessed = false;
            BigDecimal amountPaid = BigDecimal.ZERO;
            String paymentMethod = "";

            if (result.isPresent()) {
                javafx.util.Pair<String, BigDecimal> paymentData = result.get();
                paymentMethod = paymentData.getKey();
                amountPaid = paymentData.getValue();

                if (amountPaid.compareTo(BigDecimal.ZERO) > 0 && amountPaid.compareTo(transaction.getRemainingBalance()) <= 0) {
                    // Process the payment
                    // Only process full payments - partial payments no longer supported
                    if (amountPaid.compareTo(transaction.getRemainingBalance()) == 0) {
                        transaction.processFullPayment(amountPaid);
                    } else {
                        AlertUtil.showError("Full Payment Required",
                                "Only full payments are supported. Please pay the complete remaining balance: "
                                + currencyFormat.format(transaction.getRemainingBalance()));
                        return;
                    }
                    // Set payment method separately
                    transaction.setPaymentMethod(paymentMethod);
                    paymentProcessed = true;
                    System.out.println("DEBUG: Payment processed programmatically");
                }
            }

            // Check if payment was processed
            if (paymentProcessed) {
                try {
                    System.out.println("DEBUG: Payment was processed, saving transaction...");

                    // Save the transaction to database using TransactionService
                    Transaction savedTransaction = transactionService.processTransaction(transaction);
                    System.out.println("DEBUG: Transaction saved with ID: " + savedTransaction.getId());

                    // Record payment history
                    try {
                        String cashierName = "System"; // In a real system, this would be the logged-in user
                        String notes = "Payment processed via Outstanding Balances page";
                        paymentHistoryService.recordPayment(savedTransaction.getId(), amountPaid, paymentMethod, cashierName, notes);
                        System.out.println("DEBUG: Payment history recorded successfully");
                    } catch (Exception historyError) {
                        System.err.println("WARNING: Failed to record payment history: " + historyError.getMessage());
                        // Don't fail the entire payment process if history recording fails
                    }

                    // Show payment completion message (simplified since only full payments are supported)
                    String message = String.format("Payment completed successfully!\n\n"
                            + "Amount Paid: %s\n"
                            + "Payment Method: %s\n\n"
                            + "Transaction has been fully paid and removed from Outstanding Balances.",
                            NumberFormat.getCurrencyInstance().format(amountPaid),
                            paymentMethod);

                    AlertUtil.showSuccess("Payment Processed", message);

                    // Refresh the Outstanding Balances table
                    System.out.println("DEBUG: Refreshing Outstanding Balances table...");
                    loadOutstandingBalances();

                } catch (Exception e) {
                    System.err.println("ERROR: Failed to save transaction after payment:");
                    e.printStackTrace();
                    AlertUtil.showError("Database Error", "Payment was processed but failed to save to database: " + e.getMessage());
                }
            } else {
                System.out.println("DEBUG: Payment was not processed (user cancelled)");
            }

        } catch (Exception e) {
            System.err.println("ERROR: Exception in handleMakePaymentForTransaction:");
            System.err.println("ERROR: Exception type: " + e.getClass().getName());
            System.err.println("ERROR: Exception message: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
        }
    }

    /**
     * Update the total outstanding amount display
     */
    private void updateTotalOutstandingDisplay() {
        try {
            List<Transaction> outstandingTransactions = transactionDAO.findWithOutstandingBalances();
            BigDecimal totalOutstanding = outstandingTransactions.stream()
                    .map(Transaction::getRemainingBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Update the total display if the label exists
            if (lblSummary != null) {
                lblSummary.setText("Total Outstanding: " + currencyFormat.format(totalOutstanding)
                        + " (" + outstandingTransactions.size() + " transactions)");
            }
        } catch (Exception e) {
            System.err.println("Error updating total outstanding display: " + e.getMessage());
        }
    }

    private void handleViewDetailsForTransaction(Transaction transaction) {
        // Create enhanced transaction details dialog with payment history
        showTransactionDetailsWithPaymentHistory(transaction);
    }

    /**
     * Handle refund for a specific transaction
     */
    private void handleRefundForTransaction(Transaction transaction) {
        try {
            // Check if transaction has any payments to refund
            BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transaction.getId());
            BigDecimal availableForRefund = totalPaid.subtract(totalRefunded);

            if (availableForRefund.compareTo(BigDecimal.ZERO) <= 0) {
                AlertUtil.showWarning("No Refund Available",
                        "This transaction has no payments available for refund.\n\n"
                        + "Total Paid: " + currencyFormat.format(totalPaid) + "\n"
                        + "Total Refunded: " + currencyFormat.format(totalRefunded));
                return;
            }

            // Create refund dialog
            showRefundDialog(transaction, availableForRefund);

        } catch (Exception e) {
            System.err.println("Error handling refund for transaction: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Refund Error", "Failed to process refund: " + e.getMessage());
        }
    }

    /**
     * Show comprehensive transaction details with payment history
     */
    private void showTransactionDetailsWithPaymentHistory(Transaction transaction) {
        try {
            // Create a custom dialog with payment history
            javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Transaction Details - " + transaction.getTransactionNumber());
            dialog.setHeaderText("Complete Transaction Information");

            // Create main content area
            javafx.scene.layout.VBox mainContent = new javafx.scene.layout.VBox(10);
            mainContent.setPadding(new javafx.geometry.Insets(20));
            mainContent.setPrefWidth(600);
            mainContent.setPrefHeight(500);

            // Transaction basic info
            javafx.scene.layout.VBox basicInfo = createTransactionBasicInfo(transaction);

            // Payment history section
            javafx.scene.layout.VBox paymentHistorySection = createPaymentHistorySection(transaction);

            // Add sections to main content
            mainContent.getChildren().addAll(basicInfo, paymentHistorySection);

            // Create scroll pane for content
            javafx.scene.control.ScrollPane scrollPane = new javafx.scene.control.ScrollPane(mainContent);
            scrollPane.setFitToWidth(true);
            scrollPane.setPrefHeight(500);

            dialog.getDialogPane().setContent(scrollPane);
            dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);

            // Style the dialog
            dialog.getDialogPane().setStyle("-fx-background-color: #f8f9fa;");

            dialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error showing transaction details: " + e.getMessage());
            e.printStackTrace();
            // Fallback to simple details
            showSimpleTransactionDetails(transaction);
        }
    }

    /**
     * Create basic transaction information section
     */
    private javafx.scene.layout.VBox createTransactionBasicInfo(Transaction transaction) {
        javafx.scene.layout.VBox basicInfo = new javafx.scene.layout.VBox(8);
        basicInfo.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-radius: 5;");

        // Title
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Transaction Information");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Transaction details
        javafx.scene.layout.GridPane detailsGrid = new javafx.scene.layout.GridPane();
        detailsGrid.setHgap(15);
        detailsGrid.setVgap(8);

        int row = 0;
        addDetailRow(detailsGrid, row++, "Transaction #:", transaction.getTransactionNumber());
        addDetailRow(detailsGrid, row++, "Date:", transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm")));
        addDetailRow(detailsGrid, row++, "Customer:", transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer");
        addDetailRow(detailsGrid, row++, "Status:", transaction.getStatus());
        addDetailRow(detailsGrid, row++, "Total Amount:", currencyFormat.format(transaction.getTotalAmount()));
        addDetailRow(detailsGrid, row++, "Amount Paid:", currencyFormat.format(transaction.getAmountPaid()));
        addDetailRow(detailsGrid, row++, "Remaining Balance:", currencyFormat.format(transaction.getRemainingBalance()));
        addDetailRow(detailsGrid, row++, "Items Count:", String.valueOf(transaction.getItems().size()));

        basicInfo.getChildren().addAll(titleLabel, detailsGrid);
        return basicInfo;
    }

    /**
     * Add a detail row to the grid
     */
    private void addDetailRow(javafx.scene.layout.GridPane grid, int row, String label, String value) {
        javafx.scene.control.Label labelControl = new javafx.scene.control.Label(label);
        labelControl.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");

        javafx.scene.control.Label valueControl = new javafx.scene.control.Label(value);
        valueControl.setStyle("-fx-text-fill: #212529;");

        grid.add(labelControl, 0, row);
        grid.add(valueControl, 1, row);
    }

    /**
     * Create payment history section
     */
    private javafx.scene.layout.VBox createPaymentHistorySection(Transaction transaction) {
        javafx.scene.layout.VBox paymentSection = new javafx.scene.layout.VBox(10);
        paymentSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-radius: 5;");

        // Title
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Payment History");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        try {
            // Get payment history for this transaction
            List<PaymentHistory> paymentHistory = paymentHistoryService.getPaymentHistory(transaction.getId());

            if (paymentHistory.isEmpty()) {
                javafx.scene.control.Label noPaymentsLabel = new javafx.scene.control.Label("No payment history available for this transaction.");
                noPaymentsLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-style: italic;");
                paymentSection.getChildren().addAll(titleLabel, noPaymentsLabel);
            } else {
                // Create payment history table
                javafx.scene.control.TableView<PaymentHistory> paymentTable = createPaymentHistoryTable();
                paymentTable.getItems().setAll(paymentHistory);
                paymentTable.setPrefHeight(200);

                // Summary information
                javafx.scene.layout.HBox summaryBox = createPaymentSummary(transaction, paymentHistory);

                paymentSection.getChildren().addAll(titleLabel, summaryBox, paymentTable);
            }

        } catch (Exception e) {
            System.err.println("Error loading payment history: " + e.getMessage());
            e.printStackTrace();

            javafx.scene.control.Label errorLabel = new javafx.scene.control.Label("Error loading payment history: " + e.getMessage());
            errorLabel.setStyle("-fx-text-fill: #dc3545;");
            paymentSection.getChildren().addAll(titleLabel, errorLabel);
        }

        return paymentSection;
    }

    /**
     * Create payment history table
     */
    private javafx.scene.control.TableView<PaymentHistory> createPaymentHistoryTable() {
        javafx.scene.control.TableView<PaymentHistory> table = new javafx.scene.control.TableView<>();

        // Payment sequence column
        javafx.scene.control.TableColumn<PaymentHistory, String> seqCol = new javafx.scene.control.TableColumn<>("Seq");
        seqCol.setPrefWidth(50);
        seqCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getPaymentSequence())));

        // Payment date column
        javafx.scene.control.TableColumn<PaymentHistory, String> dateCol = new javafx.scene.control.TableColumn<>("Date");
        dateCol.setPrefWidth(120);
        dateCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        cellData.getValue().getPaymentDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"))));

        // Payment amount column
        javafx.scene.control.TableColumn<PaymentHistory, String> amountCol = new javafx.scene.control.TableColumn<>("Amount");
        amountCol.setPrefWidth(100);
        amountCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFormattedPaymentAmount()));

        // Payment method column with icons
        javafx.scene.control.TableColumn<PaymentHistory, String> methodCol = new javafx.scene.control.TableColumn<>("Method");
        methodCol.setPrefWidth(120);
        methodCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPaymentMethod()));

        // Add custom cell factory for payment method with icons
        methodCol.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setGraphic(null);
                    setStyle("");
                } else {
                    setText(item);

                    // Add payment method styling and icons
                    javafx.scene.layout.HBox methodBox = new javafx.scene.layout.HBox(5);
                    methodBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                    // Create method icon (using Unicode symbols)
                    javafx.scene.control.Label iconLabel = new javafx.scene.control.Label();
                    iconLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold;");

                    switch (item.toUpperCase()) {
                        case "CASH":
                            iconLabel.setText("$");
                            iconLabel.setStyle(iconLabel.getStyle() + " -fx-text-fill: #4caf50;");
                            break;
                        case "CARD":
                        case "CREDIT_CARD":
                        case "DEBIT_CARD":
                            iconLabel.setText("C");
                            iconLabel.setStyle(iconLabel.getStyle() + " -fx-text-fill: #2196f3;");
                            break;
                        case "BANK_TRANSFER":
                            iconLabel.setText("B");
                            iconLabel.setStyle(iconLabel.getStyle() + " -fx-text-fill: #ff9800;");
                            break;
                        case "MOBILE_PAYMENT":
                            iconLabel.setText("M");
                            iconLabel.setStyle(iconLabel.getStyle() + " -fx-text-fill: #9c27b0;");
                            break;
                        default:
                            iconLabel.setText("P");
                            iconLabel.setStyle(iconLabel.getStyle() + " -fx-text-fill: #607d8b;");
                            break;
                    }

                    javafx.scene.control.Label textLabel = new javafx.scene.control.Label(item);
                    textLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold;");

                    methodBox.getChildren().addAll(iconLabel, textLabel);
                    setGraphic(methodBox);
                    setText(null);
                }
            }
        });

        // Running balance column
        javafx.scene.control.TableColumn<PaymentHistory, String> runningCol = new javafx.scene.control.TableColumn<>("Running Balance");
        runningCol.setPrefWidth(120);
        runningCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getRunningBalance())));

        // Remaining balance column with enhanced formatting and color coding
        javafx.scene.control.TableColumn<PaymentHistory, String> remainingCol = new javafx.scene.control.TableColumn<>("Remaining");
        remainingCol.setPrefWidth(110);
        remainingCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getRemainingBalance())));

        // Style remaining balance column with color coding
        remainingCol.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    PaymentHistory payment = getTableView().getItems().get(getIndex());
                    BigDecimal remaining = payment.getRemainingBalance();

                    String baseStyle = "-fx-alignment: CENTER-RIGHT; -fx-font-weight: bold; -fx-padding: 5px;";
                    if (remaining.compareTo(BigDecimal.ZERO) == 0) {
                        setStyle(baseStyle + " -fx-background-color: #e8f5e8; -fx-text-fill: #2e7d32;");
                    } else if (remaining.compareTo(new BigDecimal("100")) <= 0) {
                        setStyle(baseStyle + " -fx-background-color: #fff3e0; -fx-text-fill: #f57c00;");
                    } else {
                        setStyle(baseStyle + " -fx-background-color: #ffebee; -fx-text-fill: #d32f2f;");
                    }
                }
            }
        });

        // Payment type column with enhanced styling
        javafx.scene.control.TableColumn<PaymentHistory, String> typeCol = new javafx.scene.control.TableColumn<>("Type");
        typeCol.setPrefWidth(80);
        typeCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPaymentType().toString()));

        // Style the type column to highlight refunds
        typeCol.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    if ("REFUND".equals(item)) {
                        setStyle("-fx-background-color: #ffe6e6; -fx-text-fill: #d32f2f; -fx-font-weight: bold;");
                    } else {
                        setStyle("-fx-background-color: #e8f5e8; -fx-text-fill: #2e7d32; -fx-font-weight: bold;");
                    }
                }
            }
        });

        // Details column with expand/collapse functionality
        javafx.scene.control.TableColumn<PaymentHistory, String> detailsCol = new javafx.scene.control.TableColumn<>("Details");
        detailsCol.setPrefWidth(80);
        detailsCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(""));

        detailsCol.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            private final javafx.scene.control.Button detailsButton = new javafx.scene.control.Button("Details");

            {
                detailsButton.setStyle("-fx-background-color: #e3f2fd; -fx-text-fill: #1976d2; "
                        + "-fx-border-color: #1976d2; -fx-border-radius: 3px; "
                        + "-fx-background-radius: 3px; -fx-font-size: 12px; -fx-padding: 2px 6px;");
                detailsButton.setOnAction(event -> {
                    PaymentHistory payment = getTableView().getItems().get(getIndex());
                    OutstandingBalancesController.this.showPaymentDetails(payment);
                });
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(detailsButton);
                }
            }
        });

        table.getColumns().addAll(seqCol, dateCol, amountCol, methodCol, runningCol, remainingCol, typeCol, detailsCol);

        // Style the table with enhanced appearance
        table.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1px;");
        table.setRowFactory(tv -> {
            javafx.scene.control.TableRow<PaymentHistory> row = new javafx.scene.control.TableRow<>();
            row.setOnMouseEntered(e -> {
                if (!row.isEmpty()) {
                    row.setStyle("-fx-background-color: #f0f8ff;");
                }
            });
            row.setOnMouseExited(e -> {
                if (!row.isEmpty()) {
                    row.setStyle("");
                }
            });
            return row;
        });

        return table;
    }

    /**
     * Create payment summary box
     */
    private javafx.scene.layout.HBox createPaymentSummary(Transaction transaction, List<PaymentHistory> paymentHistory) {
        javafx.scene.layout.HBox summaryBox = new javafx.scene.layout.HBox(20);
        summaryBox.setStyle("-fx-background-color: #e9ecef; -fx-padding: 10; -fx-border-radius: 3;");

        try {
            BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transaction.getId());
            int paymentCount = (int) paymentHistory.stream().filter(p -> p.getPaymentType() == PaymentHistory.PaymentType.PAYMENT).count();
            int refundCount = (int) paymentHistory.stream().filter(p -> p.getPaymentType() == PaymentHistory.PaymentType.REFUND).count();

            javafx.scene.control.Label paymentsLabel = new javafx.scene.control.Label("Payments: " + paymentCount + " (" + currencyFormat.format(totalPaid) + ")");
            paymentsLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #28a745;");

            javafx.scene.control.Label refundsLabel = new javafx.scene.control.Label("Refunds: " + refundCount + " (" + currencyFormat.format(totalRefunded) + ")");
            refundsLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #dc3545;");

            BigDecimal netAmount = totalPaid.subtract(totalRefunded);
            javafx.scene.control.Label netLabel = new javafx.scene.control.Label("Net Paid: " + currencyFormat.format(netAmount));
            netLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #007bff;");

            summaryBox.getChildren().addAll(paymentsLabel, refundsLabel, netLabel);

        } catch (Exception e) {
            javafx.scene.control.Label errorLabel = new javafx.scene.control.Label("Error calculating summary");
            errorLabel.setStyle("-fx-text-fill: #dc3545;");
            summaryBox.getChildren().add(errorLabel);
        }

        return summaryBox;
    }

    /**
     * Fallback method for simple transaction details
     */
    private void showSimpleTransactionDetails(Transaction transaction) {
        StringBuilder details = new StringBuilder();
        details.append("Transaction Details\n");
        details.append("==================\n");
        details.append("Transaction #: ").append(transaction.getTransactionNumber()).append("\n");
        details.append("Date: ").append(transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"))).append("\n");
        details.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer").append("\n");
        details.append("Status: ").append(transaction.getStatus()).append("\n\n");
        details.append("Financial Summary:\n");
        details.append("Total Amount: ").append(currencyFormat.format(transaction.getTotalAmount())).append("\n");
        details.append("Amount Paid: ").append(currencyFormat.format(transaction.getAmountPaid())).append("\n");
        details.append("Remaining Balance: ").append(currencyFormat.format(transaction.getRemainingBalance())).append("\n\n");
        details.append("Items: ").append(transaction.getItems().size()).append(" items");

        AlertUtil.showInfo("Transaction Details", details.toString());
    }

    /**
     * Show refund dialog for a transaction
     */
    private void showRefundDialog(Transaction transaction, BigDecimal availableForRefund) {
        try {
            // Create refund dialog
            javafx.scene.control.Dialog<javafx.util.Pair<String, BigDecimal>> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Process Refund - " + transaction.getTransactionNumber());
            dialog.setHeaderText("Refund Transaction Payment");

            // Set the button types
            javafx.scene.control.ButtonType refundButtonType = new javafx.scene.control.ButtonType("Process Refund", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(refundButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Create the refund form
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            // Calculate refund information (simplified without partial refund tracking)
            BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transaction.getId());
            BigDecimal availableForRefund = totalPaid.subtract(totalRefunded);

            javafx.scene.control.ComboBox<String> refundMethodCombo = new javafx.scene.control.ComboBox<>();
            refundMethodCombo.getItems().addAll("Cash", "Credit Card", "Debit Card", "Check", "Bank Transfer");
            refundMethodCombo.setValue("Cash");

            javafx.scene.control.TextField refundAmountField = new javafx.scene.control.TextField();
            refundAmountField.setPromptText("Enter refund amount");
            refundAmountField.setText(availableForRefund.toString());

            javafx.scene.control.TextArea refundReasonArea = new javafx.scene.control.TextArea();
            refundReasonArea.setPromptText("Enter refund reason (optional)");
            refundReasonArea.setPrefRowCount(2);

            // Transaction info
            grid.add(new javafx.scene.control.Label("Transaction:"), 0, 0);
            grid.add(new javafx.scene.control.Label(transaction.getTransactionNumber()), 1, 0);
            grid.add(new javafx.scene.control.Label("Total Amount:"), 0, 1);
            grid.add(new javafx.scene.control.Label(currencyFormat.format(transaction.getTotalAmount())), 1, 1);

            // Display payment and refund information
            grid.add(new javafx.scene.control.Label("Total Paid:"), 0, 2);
            grid.add(new javafx.scene.control.Label(currencyFormat.format(totalPaid)), 1, 2);
            grid.add(new javafx.scene.control.Label("Total Refunded:"), 0, 3);
            grid.add(new javafx.scene.control.Label(currencyFormat.format(totalRefunded)), 1, 3);

            grid.add(new javafx.scene.control.Label("Available for Refund:"), 0, 4);
            grid.add(new javafx.scene.control.Label(currencyFormat.format(availableForRefund)), 1, 4);
            grid.add(new javafx.scene.control.Label("Refund Method:"), 0, 5);
            grid.add(refundMethodCombo, 1, 5);
            grid.add(new javafx.scene.control.Label("Refund Amount:"), 0, 6);
            grid.add(refundAmountField, 1, 6);
            grid.add(new javafx.scene.control.Label("Refund Reason:"), 0, 7);
            grid.add(refundReasonArea, 1, 7);

            dialog.getDialogPane().setContent(grid);

            // Convert the result when the refund button is clicked
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == refundButtonType) {
                    try {
                        BigDecimal amount = new BigDecimal(refundAmountField.getText());
                        return new javafx.util.Pair<>(refundMethodCombo.getValue() + "|" + refundReasonArea.getText(), amount);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            });

            java.util.Optional<javafx.util.Pair<String, BigDecimal>> result = dialog.showAndWait();

            if (result.isPresent()) {
                javafx.util.Pair<String, BigDecimal> refundData = result.get();
                String[] methodAndReason = refundData.getKey().split("\\|", 2);
                String refundMethod = methodAndReason[0];
                String refundReason = methodAndReason.length > 1 ? methodAndReason[1] : "Refund processed via Outstanding Balances";
                BigDecimal refundAmount = refundData.getValue();

                // Validate refund amount
                if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    AlertUtil.showError("Invalid Amount", "Refund amount must be greater than zero.");
                    return;
                }

                if (refundAmount.compareTo(availableForRefund) > 0) {
                    AlertUtil.showError("Invalid Amount",
                            "Refund amount (" + currencyFormat.format(refundAmount)
                            + ") cannot exceed available amount (" + currencyFormat.format(availableForRefund) + ").");
                    return;
                }

                // Process the refund
                processRefund(transaction, refundAmount, refundMethod, refundReason);
            }

        } catch (Exception e) {
            System.err.println("Error showing refund dialog: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Dialog Error", "Failed to show refund dialog: " + e.getMessage());
        }
    }

    /**
     * Process refund for a transaction
     */
    private void processRefund(Transaction transaction, BigDecimal refundAmount, String refundMethod, String refundReason) {
        try {
            String cashierName = "System"; // In a real system, this would be the logged-in user

            // Only full refunds are supported - simplified refund processing
            BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transaction.getId());
            BigDecimal availableForRefund = totalPaid.subtract(totalRefunded);

            // Validate that this is a full refund
            if (refundAmount.compareTo(availableForRefund) != 0) {
                AlertUtil.showError("Full Refund Required",
                        "Only full refunds are supported. Please refund the complete available amount: "
                        + currencyFormat.format(availableForRefund));
                return;
            }

            // Process full refund using OutstandingBalanceRefundService
            com.clothingstore.service.OutstandingBalanceRefundService balanceRefundService
                    = com.clothingstore.service.OutstandingBalanceRefundService.getInstance();

            com.clothingstore.model.OutstandingBalanceUpdateResult refundResult
                    = balanceRefundService.processFullRefundBalanceUpdate(
                            transaction.getId(), refundAmount, refundMethod, cashierName, refundReason);

            if (refundResult.isSuccess()) {
                // Record the refund in payment history
                paymentHistoryService.recordRefund(transaction.getId(), refundAmount, refundMethod, cashierName, refundReason);

                // Show success message
                String message = String.format("Refund processed successfully!\n\n"
                        + "Transaction: %s\n"
                        + "Refund Amount: %s\n"
                        + "Refund Method: %s\n"
                        + "Refund Type: %s\n\n"
                        + "Outstanding Balances have been updated.",
                        transaction.getTransactionNumber(),
                        currencyFormat.format(refundAmount),
                        refundMethod,
                        "Full Refund");

                if (refundResult.hasWarnings()) {
                    message += "\n\nWarnings:\n" + String.join("\n", refundResult.getWarnings());
                }

                AlertUtil.showSuccess("Refund Processed", message);

                // Refresh the Outstanding Balances table
                loadOutstandingBalances();

            } else {
                AlertUtil.showError("Refund Failed", "Failed to process refund: " + refundResult.getMessage());
            }

        } catch (Exception e) {
            System.err.println("Error processing refund: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Refund Error", "Failed to process refund: " + e.getMessage());
        }
    }

    /**
     * Show detailed payment information in a popup dialog
     */
    private void showPaymentDetails(PaymentHistory payment) {
        try {
            // Create payment details dialog
            javafx.stage.Stage detailsStage = new javafx.stage.Stage();
            detailsStage.setTitle("Payment Details");
            detailsStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            detailsStage.setResizable(false);

            // Create main layout
            javafx.scene.layout.VBox mainLayout = new javafx.scene.layout.VBox(15);
            mainLayout.setPadding(new javafx.geometry.Insets(20));
            mainLayout.setStyle("-fx-background-color: #ffffff;");

            // Title
            javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Payment Details");
            titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            // Payment information grid
            javafx.scene.layout.GridPane infoGrid = new javafx.scene.layout.GridPane();
            infoGrid.setHgap(15);
            infoGrid.setVgap(10);
            infoGrid.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 15px; -fx-border-color: #dee2e6; -fx-border-radius: 5px;");

            // Add payment details
            addPaymentDetailRow(infoGrid, 0, "Payment Date:", payment.getPaymentDate().format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm:ss")));
            addPaymentDetailRow(infoGrid, 1, "Payment Amount:", String.format("$%.2f", payment.getPaymentAmount().doubleValue()));
            addPaymentDetailRow(infoGrid, 2, "Payment Method:", payment.getPaymentMethod());
            addPaymentDetailRow(infoGrid, 3, "Payment Type:", payment.getPaymentType().toString());
            addPaymentDetailRow(infoGrid, 4, "Running Balance:", String.format("$%.2f", payment.getRunningBalance().doubleValue()));
            addPaymentDetailRow(infoGrid, 5, "Remaining Balance:", String.format("$%.2f", payment.getRemainingBalance().doubleValue()));

            // Transaction context
            javafx.scene.control.Label contextLabel = new javafx.scene.control.Label("Transaction Context");
            contextLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057;");

            javafx.scene.layout.GridPane contextGrid = new javafx.scene.layout.GridPane();
            contextGrid.setHgap(15);
            contextGrid.setVgap(10);
            contextGrid.setStyle("-fx-background-color: #e9ecef; -fx-padding: 15px; -fx-border-color: #ced4da; -fx-border-radius: 5px;");

            // Get transaction details
            try {
                Optional<Transaction> transactionOpt = transactionDAO.findById(payment.getTransactionId());
                if (transactionOpt.isPresent()) {
                    Transaction transaction = transactionOpt.get();
                    addPaymentDetailRow(contextGrid, 0, "Transaction ID:", transaction.getId().toString());
                    addPaymentDetailRow(contextGrid, 1, "Transaction Date:", transaction.getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy")));
                    addPaymentDetailRow(contextGrid, 2, "Customer:", transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer");
                    addPaymentDetailRow(contextGrid, 3, "Total Amount:", String.format("$%.2f", transaction.getTotalAmount().doubleValue()));
                    addPaymentDetailRow(contextGrid, 4, "Transaction Status:", transaction.getStatus());
                }
            } catch (Exception e) {
                addPaymentDetailRow(contextGrid, 0, "Transaction Info:", "Unable to load transaction details");
            }

            // Close button
            javafx.scene.control.Button closeButton = new javafx.scene.control.Button("Close");
            closeButton.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; "
                    + "-fx-padding: 8px 20px; -fx-border-radius: 4px; -fx-background-radius: 4px;");
            closeButton.setOnAction(e -> detailsStage.close());

            javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox();
            buttonBox.setAlignment(javafx.geometry.Pos.CENTER);
            buttonBox.getChildren().add(closeButton);

            // Add all components to main layout
            mainLayout.getChildren().addAll(titleLabel, infoGrid, contextLabel, contextGrid, buttonBox);

            // Create and show scene
            javafx.scene.Scene scene = new javafx.scene.Scene(mainLayout, 450, 400);
            detailsStage.setScene(scene);
            detailsStage.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Payment Details Error", "Failed to show payment details: " + e.getMessage());
        }
    }

    /**
     * Helper method to add detail rows to payment details grid
     */
    private void addPaymentDetailRow(javafx.scene.layout.GridPane grid, int row, String label, String value) {
        javafx.scene.control.Label labelControl = new javafx.scene.control.Label(label);
        labelControl.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");

        javafx.scene.control.Label valueControl = new javafx.scene.control.Label(value);
        valueControl.setStyle("-fx-text-fill: #212529;");

        grid.add(labelControl, 0, row);
        grid.add(valueControl, 1, row);
    }
}
