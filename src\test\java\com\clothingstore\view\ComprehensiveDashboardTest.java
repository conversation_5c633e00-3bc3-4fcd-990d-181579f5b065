package com.clothingstore.view;

import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testfx.framework.junit5.ApplicationExtension;
import org.testfx.framework.junit5.Start;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for ComprehensiveDashboardController
 * 
 * Tests:
 * - FXML loading and controller initialization
 * - Programmatic UI creation
 * - Service integration
 * - Data loading functionality
 * - Navigation integration
 */
@ExtendWith(ApplicationExtension.class)
public class ComprehensiveDashboardTest {

    private ComprehensiveDashboardController controller;
    private Stage stage;

    @BeforeAll
    static void initToolkit() {
        // Initialize JavaFX toolkit if not already initialized
        if (!Platform.isFxApplicationThread()) {
            Platform.startup(() -> {});
        }
    }

    @Start
    void start(Stage stage) throws Exception {
        this.stage = stage;
        
        // Load the FXML file
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/ComprehensiveDashboard.fxml"));
        Parent root = loader.load();
        controller = loader.getController();
        
        // Create scene and show stage
        Scene scene = new Scene(root, 1200, 800);
        stage.setScene(scene);
        stage.setTitle("Comprehensive Dashboard Test");
        stage.show();
    }

    @Test
    void testFXMLLoading() {
        assertNotNull(controller, "Controller should be loaded from FXML");
        assertNotNull(controller.getMainContainer(), "Main container should be initialized");
    }

    @Test
    void testControllerInitialization() {
        // Verify controller is properly initialized
        assertNotNull(controller, "Controller should not be null");
        
        // Verify main container exists
        assertNotNull(controller.getMainContainer(), "Main container should be created");
        
        // Verify container has children (programmatically created UI)
        assertTrue(controller.getMainContainer().getChildren().size() > 0, 
            "Main container should have child components");
    }

    @Test
    void testProgrammaticUICreation() {
        // Test that programmatic UI components are created
        assertNotNull(controller.getMainContainer(), "Main container should exist");
        
        // The main container should have a scroll pane as its child
        assertTrue(controller.getMainContainer().getChildren().size() > 0, 
            "Main container should contain programmatically created components");
        
        System.out.println("DEBUG: Main container children count: " + 
            controller.getMainContainer().getChildren().size());
    }

    @Test
    void testServiceIntegration() {
        // Test that services are properly initialized
        // This is verified through the controller initialization process
        // If services fail to initialize, the controller initialization would fail
        
        assertNotNull(controller, "Controller initialization implies services are working");
        
        // Additional service verification could be added here
        System.out.println("DEBUG: Service integration test passed - controller initialized successfully");
    }

    @Test
    void testDataLoadingCapability() {
        // Test that data loading methods exist and can be called
        // This tests the structure, not the actual data loading (which requires database)
        
        assertNotNull(controller, "Controller should be available for data loading tests");
        
        // Verify that the controller has been initialized with all necessary components
        assertNotNull(controller.getMainContainer(), "Main container required for data display");
        
        System.out.println("DEBUG: Data loading capability test passed - structure is ready");
    }

    @Test
    void testNavigationIntegration() {
        // Test that navigation components are properly set up
        assertNotNull(controller, "Controller should be available for navigation tests");
        
        // Verify main container exists for navigation context
        assertNotNull(controller.getMainContainer(), "Main container required for navigation context");
        
        System.out.println("DEBUG: Navigation integration test passed - components ready");
    }

    @Test
    void testResponsiveLayout() {
        // Test that the layout adapts to different sizes
        assertNotNull(controller.getMainContainer(), "Main container should exist");
        
        // Test different stage sizes
        Platform.runLater(() -> {
            stage.setWidth(800);
            stage.setHeight(600);
        });
        
        // Verify container still exists after resize
        assertNotNull(controller.getMainContainer(), "Main container should persist after resize");
        
        System.out.println("DEBUG: Responsive layout test passed");
    }

    @Test
    void testErrorHandling() {
        // Test that the controller handles initialization gracefully
        assertNotNull(controller, "Controller should initialize without errors");
        
        // Verify that even if data loading fails, the UI structure remains intact
        assertNotNull(controller.getMainContainer(), "UI structure should remain stable");
        
        System.out.println("DEBUG: Error handling test passed - controller is robust");
    }
}
