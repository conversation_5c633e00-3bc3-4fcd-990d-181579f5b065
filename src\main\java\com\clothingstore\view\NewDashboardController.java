package com.clothingstore.view;

import com.clothingstore.dao.*;
import com.clothingstore.model.*;
import com.clothingstore.service.*;
import com.clothingstore.util.AlertUtil;
import com.clothingstore.util.NavigationUtil;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.concurrent.Task;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.sql.SQLException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.text.NumberFormat;

/**
 * Enhanced Dashboard Controller with comprehensive conditional logic and data
 * analytics
 */
public class NewDashboardController implements Initializable {

    // Header Elements
    @FXML
    private Label lblWelcomeMessage;
    @FXML
    private Label lblCurrentDateTime;
    @FXML
    private Label lblSystemStatus;
    @FXML
    private Label lblUserRole;
    @FXML
    private Label lblSeasonalMessage;

    // Quick Stats Cards
    @FXML
    private Label lblTodaySales;
    @FXML
    private Label lblTodayTransactions;
    @FXML
    private Label lblActiveCustomers;
    @FXML
    private Label lblLowStockItems;
    @FXML
    private Label lblOutstandingBalance;
    @FXML
    private Label lblPendingRefunds;

    // Performance Metrics
    @FXML
    private Label lblWeeklySales;
    @FXML
    private Label lblMonthlySales;
    @FXML
    private Label lblTopProduct;
    @FXML
    private Label lblAverageTransaction;
    @FXML
    private Label lblCustomerGrowth;
    @FXML
    private Label lblInventoryTurnover;

    // Data Tables
    @FXML
    private TableView<Transaction> tblRecentTransactions;
    @FXML
    private TableColumn<Transaction, String> colTransactionNumber;
    @FXML
    private TableColumn<Transaction, String> colTransactionDate;
    @FXML
    private TableColumn<Transaction, String> colCustomer;
    @FXML
    private TableColumn<Transaction, String> colAmount;
    @FXML
    private TableColumn<Transaction, String> colStatus;

    @FXML
    private TableView<Product> tblTopProducts;
    @FXML
    private TableColumn<Product, String> colProductName;
    @FXML
    private TableColumn<Product, String> colProductSku;
    @FXML
    private TableColumn<Product, Integer> colQuantitySold;
    @FXML
    private TableColumn<Product, String> colRevenue;

    @FXML
    private TableView<Customer> tblRecentCustomers;
    @FXML
    private TableColumn<Customer, String> colCustomerName;
    @FXML
    private TableColumn<Customer, String> colCustomerEmail;
    @FXML
    private TableColumn<Customer, String> colJoinDate;
    @FXML
    private TableColumn<Customer, String> colLoyaltyPoints;

    // Alert Sections
    @FXML
    private VBox vboxLowStockAlerts;
    @FXML
    private VBox vboxSystemAlerts;
    @FXML
    private VBox vboxOutstandingBalances;

    // Action Buttons
    @FXML
    private Button btnQuickSale;
    @FXML
    private Button btnAddProduct;
    @FXML
    private Button btnManageCustomers;
    @FXML
    private Button btnViewReports;
    @FXML
    private Button btnRefreshData;

    // Conditional Display Areas
    @FXML
    private VBox vboxManagerSection;
    @FXML
    private VBox vboxCashierSection;
    @FXML
    private VBox vboxWeekendSpecial;
    @FXML
    private VBox vboxSeasonalPromo;

    // Services and DAOs
    private TransactionDAO transactionDAO;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private PaymentHistoryDAO paymentHistoryDAO;
    private SalesAnalyticsService salesAnalyticsService;
    private CustomerAnalyticsService customerAnalyticsService;
    private LowStockAlertService lowStockAlertService;
    private OutstandingBalanceRefundService outstandingBalanceService;

    // Data Collections
    private ObservableList<Transaction> recentTransactions;
    private ObservableList<Product> topProducts;
    private ObservableList<Customer> recentCustomers;

    // Formatters
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;
    private DateTimeFormatter dateFormatter;

    // Current user context (simulated - in real app would come from authentication)
    private String currentUserRole = "MANAGER"; // MANAGER, CASHIER, ADMIN
    private String currentUserName = "System User";

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeDataCollections();

        // Load initial data
        loadDashboardData();

        // Set up conditional displays
        setupConditionalDisplays();

        // Start periodic refresh
        startPeriodicRefresh();
    }

    private void initializeServices() {
        transactionDAO = TransactionDAO.getInstance();
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        paymentHistoryDAO = PaymentHistoryDAO.getInstance();
        salesAnalyticsService = SalesAnalyticsService.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        lowStockAlertService = LowStockAlertService.getInstance();
        outstandingBalanceService = OutstandingBalanceRefundService.getInstance();
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
    }

    private void initializeDataCollections() {
        recentTransactions = FXCollections.observableArrayList();
        topProducts = FXCollections.observableArrayList();
        recentCustomers = FXCollections.observableArrayList();

        tblRecentTransactions.setItems(recentTransactions);
        tblTopProducts.setItems(topProducts);
        tblRecentCustomers.setItems(recentCustomers);
    }

    private void initializeTableColumns() {
        // Recent Transactions Table
        colTransactionNumber.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getTransactionNumber()));
        colTransactionDate.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        cellData.getValue().getTransactionDate().format(dateTimeFormatter)));
        colCustomer.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        cellData.getValue().getCustomerName() != null
                        ? cellData.getValue().getCustomerName() : "Walk-in Customer"));
        colAmount.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        currencyFormat.format(cellData.getValue().getTotalAmount())));
        colStatus.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getStatus()));

        // Top Products Table
        colProductName.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        colProductSku.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getSku()));
        // Note: Quantity sold and revenue would need additional data structure in real implementation

        // Recent Customers Table
        colCustomerName.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        cellData.getValue().getFirstName() + " " + cellData.getValue().getLastName()));
        colCustomerEmail.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPhone())); // Using phone as email not available
        colJoinDate.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        cellData.getValue().getRegistrationDate() != null
                        ? cellData.getValue().getRegistrationDate().format(dateFormatter) : "N/A"));
        colLoyaltyPoints.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(
                        String.valueOf(cellData.getValue().getLoyaltyPoints())));
    }

    /**
     * Setup conditional displays based on various factors
     */
    private void setupConditionalDisplays() {
        updateWelcomeMessage();
        updateSystemStatus();
        updateUserRoleDisplay();
        updateSeasonalMessage();
        updateConditionalSections();
    }

    /**
     * Update welcome message based on time of day
     */
    private void updateWelcomeMessage() {
        LocalTime now = LocalTime.now();
        String greeting;

        if (now.isBefore(LocalTime.of(12, 0))) {
            greeting = "Good Morning";
        } else if (now.isBefore(LocalTime.of(17, 0))) {
            greeting = "Good Afternoon";
        } else {
            greeting = "Good Evening";
        }

        lblWelcomeMessage.setText(greeting + ", " + currentUserName + "!");
        lblCurrentDateTime.setText(LocalDateTime.now().format(dateTimeFormatter));
    }

    /**
     * Update system status with database connectivity and alerts
     */
    private void updateSystemStatus() {
        try {
            // Test database connectivity
            transactionDAO.findAll(); // Simple connectivity test
            lblSystemStatus.setText("🟢 System Online");
            lblSystemStatus.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
        } catch (Exception e) {
            lblSystemStatus.setText("🔴 System Issues");
            lblSystemStatus.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
        }
    }

    /**
     * Update user role display and permissions
     */
    private void updateUserRoleDisplay() {
        lblUserRole.setText("Role: " + currentUserRole);

        // Set role-specific styling
        switch (currentUserRole) {
            case "MANAGER":
                lblUserRole.setStyle("-fx-text-fill: #8e44ad; -fx-font-weight: bold;");
                break;
            case "CASHIER":
                lblUserRole.setStyle("-fx-text-fill: #3498db; -fx-font-weight: bold;");
                break;
            case "ADMIN":
                lblUserRole.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                break;
        }
    }

    /**
     * Update seasonal message based on current month
     */
    private void updateSeasonalMessage() {
        Month currentMonth = LocalDate.now().getMonth();
        String seasonalMessage = "";

        switch (currentMonth) {
            case DECEMBER:
            case JANUARY:
            case FEBRUARY:
                seasonalMessage = "Winter Collection - Warm up your sales!";
                break;
            case MARCH:
            case APRIL:
            case MAY:
                seasonalMessage = "Spring Collection - Fresh styles arriving!";
                break;
            case JUNE:
            case JULY:
            case AUGUST:
                seasonalMessage = "Summer Sale - Beat the heat with cool deals!";
                break;
            case SEPTEMBER:
            case OCTOBER:
            case NOVEMBER:
                seasonalMessage = "Fall Fashion - Cozy up with new arrivals!";
                break;
        }

        lblSeasonalMessage.setText(seasonalMessage);
    }

    /**
     * Update conditional sections based on day of week and user role
     */
    private void updateConditionalSections() {
        DayOfWeek today = LocalDate.now().getDayOfWeek();
        boolean isWeekend = today == DayOfWeek.SATURDAY || today == DayOfWeek.SUNDAY;

        // Show/hide weekend special section
        if (vboxWeekendSpecial != null) {
            vboxWeekendSpecial.setVisible(isWeekend);
            vboxWeekendSpecial.setManaged(isWeekend);
        }

        // Show/hide role-specific sections
        if (vboxManagerSection != null) {
            boolean showManagerSection = "MANAGER".equals(currentUserRole) || "ADMIN".equals(currentUserRole);
            vboxManagerSection.setVisible(showManagerSection);
            vboxManagerSection.setManaged(showManagerSection);
        }

        if (vboxCashierSection != null) {
            boolean showCashierSection = "CASHIER".equals(currentUserRole);
            vboxCashierSection.setVisible(showCashierSection);
            vboxCashierSection.setManaged(showCashierSection);
        }
    }

    /**
     * Load comprehensive dashboard data
     */
    private void loadDashboardData() {
        Task<Void> loadDataTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                loadQuickStats();
                loadPerformanceMetrics();
                loadRecentTransactions();
                loadTopProducts();
                loadRecentCustomers();
                loadAlerts();
                return null;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    System.out.println("Dashboard data loaded successfully");
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    AlertUtil.showError("Data Loading Error",
                            "Failed to load dashboard data: " + getException().getMessage());
                });
            }
        };

        Thread loadThread = new Thread(loadDataTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }

    /**
     * Load quick statistics for dashboard cards
     */
    private void loadQuickStats() {
        try {
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.atTime(23, 59, 59);

            // Today's sales
            List<Transaction> todayTransactions = transactionDAO.findByDateRange(startOfDay, endOfDay);
            BigDecimal todaySales = todayTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Platform.runLater(() -> {
                lblTodaySales.setText(currencyFormat.format(todaySales));
                lblTodayTransactions.setText(String.valueOf(todayTransactions.size()));
            });

            // Active customers (customers with transactions in last 30 days)
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            List<Transaction> recentTransactions = transactionDAO.findByDateRange(thirtyDaysAgo, LocalDateTime.now());
            long activeCustomers = recentTransactions.stream()
                    .filter(t -> t.getCustomerId() != null)
                    .map(Transaction::getCustomerId)
                    .distinct()
                    .count();

            Platform.runLater(() -> lblActiveCustomers.setText(String.valueOf(activeCustomers)));

            // Low stock items
            List<Product> allProducts = productDAO.findAll();
            long lowStockCount = allProducts.stream()
                    .filter(p -> p.getStockQuantity() <= p.getMinStockLevel())
                    .count();

            Platform.runLater(() -> lblLowStockItems.setText(String.valueOf(lowStockCount)));

            // Outstanding balances
            List<Transaction> outstandingTransactions = transactionDAO.findWithOutstandingBalances();
            BigDecimal totalOutstanding = outstandingTransactions.stream()
                    .map(Transaction::getRemainingBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Platform.runLater(() -> lblOutstandingBalance.setText(currencyFormat.format(totalOutstanding)));

            // Pending refunds (transactions with PARTIALLY_REFUNDED status)
            long pendingRefunds = todayTransactions.stream()
                    .filter(t -> "PARTIALLY_REFUNDED".equals(t.getStatus()) || "REFUND_PENDING".equals(t.getStatus()))
                    .count();

            Platform.runLater(() -> lblPendingRefunds.setText(String.valueOf(pendingRefunds)));

        } catch (SQLException e) {
            System.err.println("Error loading quick stats: " + e.getMessage());
        }
    }

    /**
     * Load performance metrics
     */
    private void loadPerformanceMetrics() {
        try {
            LocalDate today = LocalDate.now();

            // Weekly sales (last 7 days)
            LocalDateTime weekStart = today.minusDays(7).atStartOfDay();
            List<Transaction> weeklyTransactions = transactionDAO.findByDateRange(weekStart, LocalDateTime.now());
            BigDecimal weeklySales = weeklyTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Monthly sales (current month)
            LocalDateTime monthStart = today.withDayOfMonth(1).atStartOfDay();
            List<Transaction> monthlyTransactions = transactionDAO.findByDateRange(monthStart, LocalDateTime.now());
            BigDecimal monthlySales = monthlyTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Average transaction value
            final BigDecimal averageTransaction = weeklyTransactions.isEmpty() ? BigDecimal.ZERO
                    : weeklySales.divide(BigDecimal.valueOf(weeklyTransactions.size()), 2, RoundingMode.HALF_UP);

            // Customer growth (new customers this month)
            List<Customer> allCustomers = customerDAO.findAll();
            long newCustomersThisMonth = allCustomers.stream()
                    .filter(c -> c.getRegistrationDate() != null
                    && c.getRegistrationDate().isAfter(monthStart)
                    && c.getRegistrationDate().isBefore(LocalDateTime.now()))
                    .count();

            Platform.runLater(() -> {
                lblWeeklySales.setText(currencyFormat.format(weeklySales));
                lblMonthlySales.setText(currencyFormat.format(monthlySales));
                lblAverageTransaction.setText(currencyFormat.format(averageTransaction));
                lblCustomerGrowth.setText("+" + newCustomersThisMonth + " this month");

                // Placeholder for top product and inventory turnover
                lblTopProduct.setText("Loading...");
                lblInventoryTurnover.setText("Calculating...");
            });

        } catch (SQLException e) {
            System.err.println("Error loading performance metrics: " + e.getMessage());
        }
    }

    /**
     * Load recent transactions (last 50)
     */
    private void loadRecentTransactions() {
        try {
            // Get recent transactions from today and yesterday
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime now = LocalDateTime.now();
            List<Transaction> transactions = transactionDAO.findByDateRange(yesterday, now);
            Platform.runLater(() -> {
                recentTransactions.clear();
                recentTransactions.addAll(transactions);
            });
        } catch (SQLException e) {
            System.err.println("Error loading recent transactions: " + e.getMessage());
        }
    }

    /**
     * Load top selling products
     */
    private void loadTopProducts() {
        try {
            // This would need a more sophisticated query in real implementation
            // For now, we'll load all products and sort by stock quantity (as a placeholder)
            List<Product> products = productDAO.findAll();
            List<Product> topSellingProducts = products.stream()
                    .sorted((p1, p2) -> Integer.compare(p2.getStockQuantity(), p1.getStockQuantity()))
                    .limit(10)
                    .collect(Collectors.toList());

            Platform.runLater(() -> {
                topProducts.clear();
                topProducts.addAll(topSellingProducts);
            });
        } catch (SQLException e) {
            System.err.println("Error loading top products: " + e.getMessage());
        }
    }

    /**
     * Load recent customers (last 20)
     */
    private void loadRecentCustomers() {
        try {
            List<Customer> customers = customerDAO.findAll();
            List<Customer> recentCustomersList = customers.stream()
                    .filter(c -> c.getRegistrationDate() != null)
                    .sorted((c1, c2) -> c2.getRegistrationDate().compareTo(c1.getRegistrationDate()))
                    .limit(20)
                    .collect(Collectors.toList());

            Platform.runLater(() -> {
                recentCustomers.clear();
                recentCustomers.addAll(recentCustomersList);
            });
        } catch (SQLException e) {
            System.err.println("Error loading recent customers: " + e.getMessage());
        }
    }

    /**
     * Load alerts and warnings
     */
    private void loadAlerts() {
        try {
            // Low stock alerts
            List<Product> lowStockProducts = productDAO.findAll().stream()
                    .filter(p -> p.getStockQuantity() <= p.getMinStockLevel())
                    .collect(Collectors.toList());

            Platform.runLater(() -> {
                vboxLowStockAlerts.getChildren().clear();
                for (Product product : lowStockProducts) {
                    Label alertLabel = new Label("WARNING: " + product.getName() + " - Only "
                            + product.getStockQuantity() + " left");
                    alertLabel.setStyle("-fx-text-fill: #e67e22; -fx-font-weight: bold;");
                    vboxLowStockAlerts.getChildren().add(alertLabel);
                }

                if (lowStockProducts.isEmpty()) {
                    Label noAlertsLabel = new Label("✅ All products adequately stocked");
                    noAlertsLabel.setStyle("-fx-text-fill: #27ae60;");
                    vboxLowStockAlerts.getChildren().add(noAlertsLabel);
                }
            });

        } catch (SQLException e) {
            System.err.println("Error loading alerts: " + e.getMessage());
        }
    }

    /**
     * Start periodic refresh of dashboard data
     */
    private void startPeriodicRefresh() {
        Timer refreshTimer = new Timer(true);
        refreshTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    updateWelcomeMessage();
                    updateSystemStatus();
                    // Refresh data every 30 seconds
                    loadDashboardData();
                });
            }
        }, 30000, 30000); // 30 seconds initial delay, then every 30 seconds
    }

    // Event Handlers
    @FXML
    private void handleQuickSale() {
        try {
            NavigationUtil.navigateToPointOfSale(btnQuickSale);
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Point of Sale: " + e.getMessage());
        }
    }

    @FXML
    private void handleAddProduct() {
        try {
            NavigationUtil.navigateToProductManagement(btnAddProduct);
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Product Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleManageCustomers() {
        try {
            NavigationUtil.navigateToCustomerManagement(btnManageCustomers);
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Customer Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleViewReports() {
        try {
            NavigationUtil.navigateTo(btnViewReports, "Reports.fxml", "Reports");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Reports: " + e.getMessage());
        }
    }

    @FXML
    private void handleRefreshData() {
        try {
            btnRefreshData.setText("Refreshing...");
            btnRefreshData.setDisable(true);

            loadDashboardData();
            setupConditionalDisplays();

            Platform.runLater(() -> {
                btnRefreshData.setText("Refresh Data");
                btnRefreshData.setDisable(false);
                AlertUtil.showInfo("Refresh Complete", "Dashboard data has been refreshed successfully.");
            });
        } catch (Exception e) {
            Platform.runLater(() -> {
                btnRefreshData.setText("Refresh Data");
                btnRefreshData.setDisable(false);
            });
            AlertUtil.showError("Refresh Error", "Failed to refresh dashboard data: " + e.getMessage());
        }
    }

    /**
     * Get customer name from transaction
     */
    private String getCustomerNameFromTransaction(Transaction transaction) {
        if (transaction.getCustomer() != null) {
            Customer customer = transaction.getCustomer();
            return customer.getFirstName() + " " + customer.getLastName();
        } else if (transaction.getCustomerName() != null) {
            return transaction.getCustomerName();
        } else {
            return "Walk-in Customer";
        }
    }
}
