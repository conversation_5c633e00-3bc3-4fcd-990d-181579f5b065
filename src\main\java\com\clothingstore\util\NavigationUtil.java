package com.clothingstore.util;

import java.io.IOException;

import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.layout.StackPane;
import javafx.stage.Stage;

/**
 * Utility class for navigation between different views in the application
 */
public class NavigationUtil {

    /**
     * Navigate to a specific FXML page by loading it into the main content area
     *
     * @param sourceNode Any node from the current scene (used to find the main
     * content area)
     * @param fxmlFile The FXML file to load (e.g., "ProductManagement.fxml")
     * @param title The title for status updates
     */
    public static void navigateTo(Node sourceNode, String fxmlFile, String title) {
        try {
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(NavigationUtil.class.getResource("/fxml/" + fxmlFile));
            Parent content = loader.load();

            // Find the main content area (StackPane with fx:id="contentArea")
            StackPane contentArea = findContentArea(sourceNode);
            if (contentArea != null) {
                contentArea.getChildren().clear();
                contentArea.getChildren().add(content);
                System.out.println("Successfully navigated to: " + title);
            } else {
                AlertUtil.showError("Navigation Error", "Could not find main content area for navigation.");
            }

        } catch (IOException e) {
            AlertUtil.showError("Loading Error", "Failed to load " + title + ": " + e.getMessage());
            System.err.println("Navigation error: " + e.getMessage());
        }
    }

    /**
     * Find the main content area (StackPane) in the scene graph
     *
     * @param sourceNode Any node from the current scene
     * @return The main content StackPane or null if not found
     */
    private static StackPane findContentArea(Node sourceNode) {
        try {
            // Get the stage from the source node
            Stage stage = (Stage) sourceNode.getScene().getWindow();
            if (stage != null && stage.getScene() != null) {
                // Look for the StackPane with fx:id="contentArea" in the scene
                return findStackPaneById(stage.getScene().getRoot(), "contentArea");
            }
        } catch (Exception e) {
            System.err.println("Error finding content area: " + e.getMessage());
        }
        return null;
    }

    /**
     * Recursively search for a StackPane with a specific fx:id
     *
     * @param node The node to search from
     * @param id The fx:id to search for
     * @return The StackPane with the specified id or null if not found
     */
    private static StackPane findStackPaneById(Node node, String id) {
        // Check if this node is a StackPane with the correct id
        if (node instanceof StackPane && id.equals(node.getId())) {
            return (StackPane) node;
        }

        // If this node is a Parent, search its children
        if (node instanceof Parent) {
            Parent parent = (Parent) node;
            for (Node child : parent.getChildrenUnmodifiable()) {
                StackPane result = findStackPaneById(child, id);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * Navigate to Product Management page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToProductManagement(Node sourceNode) {
        navigateTo(sourceNode, "ProductManagement.fxml", "Product Management");
    }

    /**
     * Navigate to Customer Management page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToCustomerManagement(Node sourceNode) {
        navigateTo(sourceNode, "CustomerManagement.fxml", "Customer Management");
    }

    /**
     * Navigate to Point of Sale page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToPointOfSale(Node sourceNode) {
        navigateTo(sourceNode, "PointOfSaleNew.fxml", "Point of Sale");
    }

    /**
     * Navigate to Sales Report page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToSalesReport(Node sourceNode) {
        navigateTo(sourceNode, "SalesReport.fxml", "Sales Report");
    }

    /**
     * Navigate to Transaction History page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToTransactionHistory(Node sourceNode) {
        navigateTo(sourceNode, "TransactionHistory.fxml", "Transaction History");
    }

    /**
     * Navigate to Dashboard page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToDashboard(Node sourceNode) {
        try {
            // Try new dashboard first, fallback to old dashboard
            navigateTo(sourceNode, "NewDashboardSimple.fxml", "Dashboard");
        } catch (Exception e) {
            System.err.println("New Dashboard failed, falling back to old dashboard: " + e.getMessage());
            navigateTo(sourceNode, "Dashboard.fxml", "Dashboard");
        }
    }

    /**
     * Navigate to Settings page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToSettings(Node sourceNode) {
        navigateTo(sourceNode, "Settings.fxml", "Settings");
    }

    /**
     * Navigate to Supplier Management page
     *
     * @param sourceNode Any node from the current scene
     */
    public static void navigateToSupplierManagement(Node sourceNode) {
        navigateTo(sourceNode, "SupplierManagement.fxml", "Supplier Management");
    }
}
